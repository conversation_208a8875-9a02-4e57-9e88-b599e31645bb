name: Generate Embeddings for Search

on:
  push:
    branches:
      - master
    paths:
      - '.github/workflows/search.yml'
      - 'supabase/migrations/**'
      - 'apps/docs/**'
  workflow_dispatch:
    inputs:
      refresh:
        description: 'Refresh all pages'
        required: false
        type: boolean
  schedule:
    - cron: '0 0 * * *'

concurrency:
  group: ${{ github.workflow }}-prod
  cancel-in-progress: false

permissions:
  contents: read

jobs:
  deploy:
    runs-on: ubuntu-latest

    env:
      DOCS_GITHUB_APP_ID: ${{ secrets.SEARCH_GITHUB_APP_ID }}
      DOCS_GITHUB_APP_INSTALLATION_ID: ${{ secrets.SEARCH_GITHUB_APP_INSTALLATION_ID }}
      DOCS_GITHUB_APP_PRIVATE_KEY: ${{ secrets.SEARCH_GITHUB_APP_PRIVATE_KEY }}
      NEXT_PUBLIC_MISC_URL: ${{ secrets.NEXT_PUBLIC_MISC_URL}}
      NEXT_PUBLIC_MISC_ANON_KEY: ${{ secrets.NEXT_PUBLIC_MISC_ANON_KEY }}
      NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.SEARCH_SUPABASE_URL }}
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      SUPABASE_SECRET_KEY: ${{ secrets.SEARCH_SUPABASE_SERVICE_ROLE_KEY }}

    steps:
      - name: Check out repo
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            apps/docs
            apps/www/.env.local.example
            supabase

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'

      - name: Download dependencies
        run: pnpm i

      - name: Update embeddings
        working-directory: ./apps/docs
        if: ${{ !inputs.refresh }}
        run: pnpm run embeddings

      - name: Refresh embeddings
        working-directory: ./apps/docs
        if: ${{ inputs.refresh }}
        run: pnpm run embeddings:refresh
