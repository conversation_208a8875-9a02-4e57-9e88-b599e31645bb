name: Deploy OG Images

on:
  push:
    branches:
      - master
    paths:
      - 'supabase/functions/og-images/**'
  workflow_dispatch:

permissions:
  contents: read

jobs:
  deploy:
    runs-on: ubuntu-latest

    env:
      SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
      PROJECT_ID: ${{ secrets.OG_IMAGE_SUPABASE_PROJECT_ID }}

    steps:
      - name: Check out repo
        uses: actions/checkout@v4

      - name: Setup the Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest

      - run: supabase functions deploy og-images --project-ref $PROJECT_ID --no-verify-jwt
