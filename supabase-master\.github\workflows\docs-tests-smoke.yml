# Smoke tests against the docs production site, run daily

name: Docs Production Smoke Tests

on:
  workflow_dispatch:
  schedule:
    - cron: '0 4 * * *'

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          sparse-checkout: |
            apps/docs
            packages

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'pnpm'

      - name: Install deps
        run: pnpm i

      - name: Run tests
        run: pnpm --prefix="apps/docs" run test:smoke
