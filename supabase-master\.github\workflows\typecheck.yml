name: Check code with TypeScript & Lint

on:
  pull_request:
    branches:
      - 'master'

# Cancel old builds on new commit for same workflow + branch/PR
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  typecheck:
    # Uses larger hosted runner as it significantly decreases build times
    runs-on:
      group: Default Larger Runners

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'pnpm'

      - name: Install deps
        run: pnpm i

      - name: Run TypeScript type check
        run: pnpm exec turbo run typecheck

      - name: Run Lint
        run: pnpm exec turbo run lint
