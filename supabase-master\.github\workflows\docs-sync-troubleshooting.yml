name: '[Docs] Sync troubleshooting guides to GitHub Discussions'

on:
  push:
    branches:
      - master
    paths:
      - 'apps/docs/content/troubleshooting/**'
  workflow_dispatch:

permissions:
  contents: write
  pull-requests: write

jobs:
  update-troubleshooting:
    runs-on: ubuntu-latest

    env:
      DOCS_GITHUB_APP_ID: ${{ secrets.SEARCH_GITHUB_APP_ID }}
      DOCS_GITHUB_APP_INSTALLATION_ID: ${{ secrets.SEARCH_GITHUB_APP_INSTALLATION_ID }}
      DOCS_GITHUB_APP_PRIVATE_KEY: ${{ secrets.SEARCH_GITHUB_APP_PRIVATE_KEY }}
      NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.SEARCH_SUPABASE_URL }}
      SUPABASE_SECRET_KEY: ${{ secrets.SEARCH_SUPABASE_SERVICE_ROLE_KEY }}

    steps:
      - uses: actions/checkout@v4
        with:
          sparse-checkout: |
            apps/docs

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm i

      - name: Run Troubleshooting script
        run: pnpm run -F docs troubleshooting:sync

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@5e914681df9dc83aa4e4905692ca88beb2f9e91f # v7.0.5
        with:
          commit-message: '[bot] sync troubleshooting guides to db'
          title: '[bot] sync troubleshooting guides to db'
          author: 'github-docs-sync-bot <<EMAIL>>'
          branch: 'bot/docs-sync-troubleshooting'
          branch-suffix: 'random'
