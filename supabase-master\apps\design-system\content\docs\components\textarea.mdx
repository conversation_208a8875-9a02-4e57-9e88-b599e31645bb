---
title: Textarea
description: Displays a form textarea or a component that looks like a textarea.
component: true
source:
  shadcn: true
---

<ComponentPreview name="textarea-demo" peekCode wide />

## Installation

<Tabs defaultValue="cli">

<TabsList>
  <TabsTrigger value="cli">CLI</TabsTrigger>
  <TabsTrigger value="manual">Manual</TabsTrigger>
</TabsList>
<TabsContent value="cli">

```bash
npx shadcn-ui@latest add textarea
```

</TabsContent>

<TabsContent value="manual">

<Steps>

<Step>Copy and paste the following code into your project.</Step>

<ComponentSource name="textarea" />

<Step>Update the import paths to match your project setup.</Step>

</Steps>

</TabsContent>

</Tabs>

## Usage

```tsx
import { Textarea } from '@/components/ui/textarea'
```

```tsx
<Textarea />
```

## Examples

### Default

<ComponentPreview name="textarea-demo" peekCode wide />

### Disabled

<ComponentPreview name="textarea-disabled" />

### With Label

<ComponentPreview name="textarea-with-label" className="[&_div.grid]:w-full" />

### With Text

<ComponentPreview name="textarea-with-text" />

### With Button

<ComponentPreview name="textarea-with-button" />

### Form

<ComponentPreview name="textarea-form" />
