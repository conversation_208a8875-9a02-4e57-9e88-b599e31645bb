name: reviewdog
on:
  pull_request:
    types: [opened, synchronize, reopened, edited]

# Cancel old builds on new commit for same workflow + branch/PR
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  misspell:
    name: runner / misspell
    runs-on: ubuntu-latest
    steps:
      - name: Check out code.
        uses: actions/checkout@v4
      - name: misspell
        uses: reviewdog/action-misspell@v1
        with:
          github_token: ${{ secrets.github_token }}
          locale: 'US'
          reporter: github-pr-review
          level: error
          exclude: |
            *.css
            **/package.json
            **/pnpm-lock.yaml
            ./.git/*
            *.ipynb
            ./i18n/README.*.md
            ./studio/public/monaco-editor/*
