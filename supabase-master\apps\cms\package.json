{"name": "cms", "version": "1.0.0", "description": "Payload CMS for Supabase", "license": "MIT", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build --turbopack", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev --turbopack --port 3030", "devsafe": "rm -rf .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "start": "cross-env NODE_OPTIONS=--no-deprecation next start", "ci": "cross-env NODE_OPTIONS=--no-deprecation payload migrate && pnpm build", "clean": "rimraf node_modules .next", "typecheck_IGNORED": "tsc --noEmit"}, "dependencies": {"@payloadcms/db-postgres": "3.33.0", "@payloadcms/live-preview-react": "^3.33.0", "@payloadcms/next": "3.33.0", "@payloadcms/payload-cloud": "3.33.0", "@payloadcms/plugin-form-builder": "3.33.0", "@payloadcms/plugin-nested-docs": "3.33.0", "@payloadcms/plugin-seo": "3.33.0", "@payloadcms/richtext-lexical": "3.33.0", "@payloadcms/storage-s3": "3.33.0", "@payloadcms/ui": "3.33.0", "common": "workspace:*", "config": "workspace:*", "cross-env": "^7.0.3", "eslint-config-supabase": "workspace:*", "graphql": "^16.8.1", "next": "catalog:", "payload": "3.33.0", "react": "catalog:", "react-dom": "catalog:", "sharp": "0.32.6"}, "devDependencies": {"@types/node": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "typescript": "5.7.3"}}