name: '[Docs] Update last-changed dates'

on:
  schedule:
    - cron: '0 0 * * *'
  workflow_dispatch:
    inputs:
      reset:
        description: 'Reset last-updated dates using Git commit dates'
        required: false
        type: boolean

permissions:
  contents: read

jobs:
  deploy:
    runs-on: ubuntu-latest

    env:
      NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.SEARCH_SUPABASE_URL }}
      SUPABASE_SECRET_KEY: ${{ secrets.SEARCH_SUPABASE_SERVICE_ROLE_KEY }}

    steps:
      - name: Check out repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          sparse-checkout: |
            apps/docs

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'

      - name: Download dependencies
        run: pnpm i

      - name: Update last-changed dates
        working-directory: ./apps/docs
        if: ${{ !inputs.reset }}
        run: pnpm run last-changed

      - name: Reset last-changed dates
        working-directory: ./apps/docs
        if: ${{ inputs.reset }}
        run: pnpm run last-changed:reset
