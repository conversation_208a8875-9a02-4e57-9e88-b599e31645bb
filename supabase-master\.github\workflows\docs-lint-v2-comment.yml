name: docs_lint_comment_external

# This is a continuation of ./docs-lint-v2.yml, to write comments on external
# PRs.
#
# SECURITY:
# This workflow runs with write permissions, in the context of code from an
# external PR. This is safe because no external code is executed. The
# stringified Markdown output from the linter (downloaded as an artifact) is
# directly written as the body of a PR comment.

on:
  workflow_run:
    workflows: [docs_lint]
    types:
      - completed

permissions:
  pull-requests: write

jobs:
  comment_on_pr:
    runs-on: ubuntu-latest
    if: github.event.workflow_run.event == 'pull_request' && github.event.workflow_run.conclusion == 'failure'
    steps:
      - id: download_artifact
        name: 'Download artifact'
        uses: actions/github-script@v7
        with:
          script: |
            const artifacts = await github.rest.actions.listWorkflowRunArtifacts({
              owner: context.repo.owner,
              repo: context.repo.repo,
              run_id: ${{ github.event.workflow_run.id }}
            });
            const matchingArtifact = artifacts?.data?.artifacts?.find(
              (artifact) => artifact.name == 'lint_results'
            );
            if (matchingArtifact) {
              core.setOutput('contains_results', 'true')
              const download = await github.rest.actions.downloadArtifact({
                owner: context.repo.owner,
                repo: context.repo.repo,
                artifact_id: matchingArtifact.id,
                archive_format: 'zip',
              });
              const fs = require('fs');
              fs.writeFileSync('${{ github.workspace }}/lint_results.zip', Buffer.from(download.data));
            }
      - id: unzip_results
        name: Unzip results file
        if: steps.download_artifact.outputs.contains_results == 'true'
        run: unzip lint_results.zip
      - name: 'Comment on PR'
        if: steps.download_artifact.outputs.contains_results == 'true'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const prNumber = Number(fs.readFileSync('./pr_number.txt'));
            const lintResults = fs.readFileSync('./lint_results.txt', 'utf8');
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: prNumber,
              body: lintResults
            });
