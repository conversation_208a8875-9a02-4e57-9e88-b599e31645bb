name: Mirror Image

on:
  workflow_call:
    inputs:
      version:
        required: true
        type: string
  workflow_dispatch:
    inputs:
      version:
        description: 'Image tag'
        required: true
        type: string

jobs:
  mirror:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
      id-token: write
    steps:
      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: ${{ secrets.PROD_AWS_ROLE }}
          aws-region: us-east-1
      - uses: docker/login-action@v2
        with:
          registry: public.ecr.aws
      - uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - uses: akhilerm/tag-push-action@v2.1.0
        with:
          src: docker.io/supabase/studio:${{ inputs.version }}
          dst: |
            public.ecr.aws/supabase/studio:${{ inputs.version }}
            ghcr.io/supabase/studio:${{ inputs.version }}
