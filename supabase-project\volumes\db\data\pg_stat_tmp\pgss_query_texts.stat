WITH pgrst_source AS (SELECT pgrst_call.pgrst_scalar FROM (SELECT $1 AS json_data) pgrst_payload, LATERAL (SELECT "user_uuid", "deduct_amount", "access_password" FROM json_to_record(pgrst_payload.json_data) AS _("user_uuid" uuid, "deduct_amount" integer, "access_password" text) LIMIT $2) pgrst_body , LATERAL (SELECT "public"."deduct_daily_free_quota"("user_uuid" := pgrst_body."user_uuid", "deduct_amount" := pgrst_body."deduct_amount", "access_password" := pgrst_body."access_password") pgrst_scalar) pgrst_call) SELECT $3::bigint AS total_result_set, $4 AS page_total, coalesce(json_agg(_postgrest_t.pgrst_scalar)->$5, $6) AS body, nullif(current_setting($7, $8), $9) AS response_headers, nullif(current_setting($10, $11), $12) AS response_status, $13 AS response_inserted FROM (SELECT "deduct_daily_free_quota".* FROM "pgrst_source" AS "deduct_daily_free_quota"     ) _postgrest_t SELECT 
  column_name,
  data_type,
  column_default
FROM information_schema.columns 
WHERE table_name = $1 
AND (column_name = $2 OR column_name = $3 OR column_name = $4) INSERT INTO "_analytics"."log_events_7915a5ef_6f63_4d10_8209_3453b035cc75" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12) -- 测试扣费函数
SELECT deduct_daily_free_quota(
  $1::UUID,
  $2,
  $3
) INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84) INSERT INTO "_analytics"."log_events_7915a5ef_6f63_4d10_8209_3453b035cc75" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r with _base_query as (select * from public."propmt-zhanshi" order by "propmt-zhanshi".id asc nulls last limit $1 offset $2)
  select id,case
        when octet_length(title::text) > $3 
        then left(title::text, $4) || $5
        else title::text
      end as title,case
        when octet_length(description::text) > $6 
        then left(description::text, $7) || $8
        else description::text
      end as description,case
        when octet_length(category::text) > $9 
        then left(category::text, $10) || $11
        else category::text
      end as category,case
        when octet_length(type::text) > $12 
        then left(type::text, $13) || $14
        else type::text
      end as type,created_at,updated_at,case
        when octet_length(created_by::text) > $15 
        then left(created_by::text, $16) || $17
        else created_by::text
      end as created_by,case
        when octet_length(author_display_id::text) > $18 
        then left(author_display_id::text, $19) || $20
        else author_display_id::text
      end as author_display_id,usage_count from _base_query SELECT s0."id", s0."all_logs_logged", s0."node", s0."inserted_at", s0."updated_at" FROM "system_metrics" AS s0 WHERE (s0."node" = $1) -- reports-query-performance-slowest-execution-time
set search_path to public, extensions SELECT u0."id", u0."db_user_alias", u0."db_user", u0."db_pass_encrypted", u0."is_manager", u0."mode_type", u0."pool_size", u0."pool_checkout_timeout", u0."max_clients", u0."tenant_external_id", u0."inserted_at", u0."updated_at", u0."tenant_external_id" FROM "_supavisor"."users" AS u0 WHERE (u0."tenant_external_id" = $1) ORDER BY u0."tenant_external_id" SELECT deduct_user_word_count($1, $2, $3) -- 查看剩余的重置相关函数
SELECT proname FROM pg_proc WHERE proname LIKE $1 OR proname LIKE $2 SELECT EXTRACT($1 FROM (current_timestamp - pg_postmaster_start_time())) -- 创建每日19:10分执行的重置任务
SELECT cron.schedule(
  $1, 
  $2, 
  $3
) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- 查看数据库中的表结构
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = $1 
AND table_name LIKE $2
ORDER BY table_name NOTIFY supavisor_local_2_5, 'nonode@nohost' SELECT name, setting, unit, context FROM pg_settings WHERE name LIKE $1 -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:13:13.153Z

set local search_path = '' with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112),($113,$114,$115,$116),($117,$118,$119,$120),($121,$122,$123,$124),($125,$126,$127,$128),($129,$130,$131,$132),($133,$134,$135,$136),($137,$138,$139,$140),($141,$142,$143,$144),($145,$146,$147,$148),($149,$150,$151,$152),($153,$154,$155,$156),($157,$158,$159,$160),($161,$162,$163,$164),($165,$166,$167,$168),($169,$170,$171,$172),($173,$174,$175,$176),($177,$178,$179,$180) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r SELECT o0."id", o0."token", o0."refresh_token", o0."expires_in", o0."revoked_at", o0."scopes", o0."previous_refresh_token", o0."resource_owner_id", o0."application_id", o0."description", o0."inserted_at", o0."updated_at" FROM "oauth_access_tokens" AS o0 WHERE (o0."token" = $1) INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40) CREATE OR REPLACE FUNCTION pg_temp.pg_get_coldef(in_schema text,in_table text,in_column text,oldway boolean default False)RETURNS text LANGUAGE plpgsql VOLATILE AS $$ DECLARE v_coldef text;v_dt1 text;v_dt2 text;v_dt3 text;v_nullable boolean;v_position int;v_identity text;v_generated text;v_hasdflt boolean;v_dfltexpr text;BEGIN IF oldway THEN SELECT pg_catalog.format_type(a.atttypid,a.atttypmod)INTO v_coldef FROM pg_namespace n,pg_class c,pg_attribute a,pg_type t WHERE n.nspname=in_schema AND n.oid=c.relnamespace AND c.relname=in_table AND a.attname=in_column and a.attnum>0 AND a.attrelid=c.oid AND a.atttypid=t.oid ORDER BY a.attnum;ELSE SELECT CASE WHEN a.atttypid=ANY('{int,int8,int2}'::regtype[])AND EXISTS(SELECT FROM pg_attrdef ad WHERE ad.adrelid=a.attrelid AND ad.adnum=a.attnum AND pg_get_expr(ad.adbin,ad.adrelid)='nextval('''||(pg_get_serial_sequence(a.attrelid::regclass::text,a.attname))::regclass||'''::regclass)')THEN CASE a.atttypid WHEN'int'::regtype THEN'serial'WHEN'int8'::regtype THEN'bigserial'WHEN'int2'::regtype THEN'smallserial'END ELSE format_type(a.atttypid,a.atttypmod)END AS data_type INTO v_coldef FROM pg_namespace n,pg_class c,pg_attribute a,pg_type t WHERE n.nspname=in_schema AND n.oid=c.relnamespace AND c.relname=in_table AND a.attname=in_column and a.attnum>0 AND a.attrelid=c.oid AND a.atttypid=t.oid ORDER BY a.attnum;END IF;RETURN v_coldef;END;$$ SELECT a0."id", a0."name", a0."description", a0."language", a0."query", a0."cron", a0."source_mapping", a0."token", a0."slack_hook_url", a0."webhook_notification_url", a0."user_id", a0."inserted_at", a0."updated_at" FROM "alert_queries" AS a0 INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112),($113,$114,$115,$116),($117,$118,$119,$120),($121,$122,$123,$124),($125,$126,$127,$128),($129,$130,$131,$132),($133,$134,$135,$136),($137,$138,$139,$140) CREATE TABLE IF NOT EXISTS "schema_migrations" ("version" bigint, "inserted_at" timestamp(0), PRIMARY KEY ("version")) -- 创建修改版的作者奖励函数，只在使用每日免费额度时添加奖励
CREATE OR REPLACE FUNCTION add_author_daily_quota_for_prompt_usage(
  user_uuid_param UUID,
  prompt_id_param UUID,
  access_password TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  author_user_id UUID;
  uuid_string TEXT;
  prompt_uuid_string TEXT;
BEGIN
  -- 密码校验
  IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
    RAISE EXCEPTION 'Invalid access password';
  END IF;
  
  -- 参数验证
  IF user_uuid_param IS NULL THEN
    RAISE EXCEPTION 'user_uuid_param cannot be null';
  END IF;
  
  IF prompt_id_param IS NULL THEN
    RAISE EXCEPTION 'prompt_id_param cannot be null';
  END IF;
  
  -- UUID格式验证
  uuid_string := user_uuid_param::text;
  IF LENGTH(uuid_string) != 36 OR 
     uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid user UUID format';
  END IF;
  
  prompt_uuid_string := prompt_id_param::text;
  IF LENGTH(prompt_uuid_string) != 36 OR 
     prompt_uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid prompt UUID format';
  END IF;
  
  -- 获取提示词作者的user_id
  SELECT created_by INTO author_user_id
  FROM "propmt-zhanshi"
  WHERE id = prompt_id_param;
  
  -- 如果提示词不存在
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- 如果是作者自己使用自己的提示词，不给奖励
  IF author_user_id = user_uuid_param THEN
    RETURN FALSE;
  END IF;
  
  -- 为作者添加1次奖励额度
  UPDATE "membership-look"
  SET 
    reward_quota = COALESCE(reward_quota, 0) + 1,
    updated_at = NOW()
  WHERE user_id = author_user_id AND is_verified = TRUE;
  
  -- 检查是否更新成功
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql -- 删除错误的数据库函数
DROP FUNCTION IF EXISTS deduct_daily_free_quota(UUID, INTEGER, TEXT) LOCK TABLE "schema_migrations" IN SHARE UPDATE EXCLUSIVE MODE select version() WITH pgrst_source AS ( SELECT "public"."membership-look"."is_verified", "public"."membership-look"."daily_free_quota", "public"."membership-look"."daily_free_used", "public"."membership-look"."last_free_reset_date" FROM "public"."membership-look"  WHERE  "public"."membership-look"."user_id" = $1    )  SELECT $2::bigint AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, coalesce(json_agg(_postgrest_t)->$3, $4) AS body, nullif(current_setting($5, $6), $7) AS response_headers, nullif(current_setting($8, $9), $10) AS response_status, $11 AS response_inserted FROM ( SELECT * FROM pgrst_source ) _postgrest_t SELECT 
  daily_free_used, 
  daily_free_quota, 
  daily_free_quota - daily_free_used as remaining_daily,
  reward_used, 
  reward_quota,
  reward_quota - reward_used as remaining_reward,
  last_free_reset_date,
  CURRENT_DATE as today
FROM "membership-true" 
WHERE user_id = $1 SELECT u0."id", u0."email", u0."provider", u0."token", u0."api_key", u0."old_api_key", u0."email_preferred", u0."name", u0."image", u0."email_me_product", u0."admin", u0."phone", u0."bigquery_project_id", u0."bigquery_dataset_location", u0."bigquery_dataset_id", u0."bigquery_udfs_hash", u0."bigquery_processed_bytes_limit", u0."api_quota", u0."valid_google_account", u0."provider_uid", u0."company", u0."billing_enabled", u0."endpoints_beta", u0."metadata", u0."preferences", u0."partner_upgraded", u0."partner_id", u0."inserted_at", u0."updated_at" FROM "users" AS u0 WHERE (u0."id" = $1) -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:27:29.882Z

set local search_path = '' SELECT o0."id", o0."token", o0."refresh_token", o0."expires_in", o0."revoked_at", o0."scopes", o0."previous_refresh_token", o0."resource_owner_id", o0."application_id", o0."description", o0."inserted_at", o0."updated_at" FROM "oauth_access_tokens" AS o0 WHERE ((o0."resource_owner_id" = $1) AND (o0."revoked_at" IS NULL)) AND (NOT (o0."scopes" ILIKE $2)) SELECT * FROM cron.job ORDER BY jobid with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r INSERT INTO mfa_amr_claims(id, session_id, created_at, updated_at, authentication_method) values ($1, $2, $3, $4, $5)
			ON CONFLICT ON CONSTRAINT mfa_amr_claims_session_id_authentication_method_pkey
			DO UPDATE SET updated_at = $6 select version() COMMIT -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:11:27.335Z

set local search_path = '' -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:21:02.000Z

set local search_path = '' with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r with _base_query as (select * from public."propmt-neirong" order by "propmt-neirong".prompt_id asc nulls last limit $1 offset $2)
  select prompt_id,case
        when octet_length(content::text) > $3 
        then left(content::text, $4) || $5
        else content::text
      end as content,created_at,updated_at,case
        when octet_length(title::text) > $6 
        then left(title::text, $7) || $8
        else title::text
      end as title,case
        when octet_length(author_display_id::text) > $9 
        then left(author_display_id::text, $10) || $11
        else author_display_id::text
      end as author_display_id,usage_count from _base_query -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:16:25.419Z

set local search_path = '' -- 确认测试函数已删除
SELECT COUNT(*) as remaining_functions 
FROM pg_proc 
WHERE proname = $1 -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:41:41.708Z

set local search_path = '' SELECT pg_backend_pid() with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r BEGIN ISOLATION LEVEL READ COMMITTED READ WRITE -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:29:02.288Z

set local search_path = '' WITH
  columns AS (
      SELECT
          nc.nspname::name AS table_schema,
          c.relname::name AS table_name,
          a.attname::name AS column_name,
          d.description AS description,
  
          CASE
            WHEN (t.typbasetype != $2) AND (ad.adbin IS NULL) THEN pg_get_expr(t.typdefaultbin, $3)
            WHEN a.attidentity  = $4 THEN format($5, quote_literal(seqsch.nspname || $6 || seqclass.relname))
            WHEN a.attgenerated = $7 THEN $8
            ELSE pg_get_expr(ad.adbin, ad.adrelid)::text
          END AS column_default,
          not (a.attnotnull OR t.typtype = $9 AND t.typnotnull) AS is_nullable,
          CASE
              WHEN t.typtype = $10 THEN
              CASE
                  WHEN nbt.nspname = $11::name THEN format_type(t.typbasetype, $12::integer)
                  ELSE format_type(a.atttypid, a.atttypmod)
              END
              ELSE
              CASE
                  WHEN nt.nspname = $13::name THEN format_type(a.atttypid, $14::integer)
                  ELSE format_type(a.atttypid, a.atttypmod)
              END
          END::text AS data_type,
          format_type(a.atttypid, a.atttypmod)::text AS nominal_data_type,
          information_schema._pg_char_max_length(
              information_schema._pg_truetypid(a.*, t.*),
              information_schema._pg_truetypmod(a.*, t.*)
          )::integer AS character_maximum_length,
          COALESCE(bt.oid, t.oid) AS base_type,
          a.attnum::integer AS position
      FROM pg_attribute a
          LEFT JOIN pg_description AS d
              ON d.objoid = a.attrelid and d.objsubid = a.attnum
          LEFT JOIN pg_attrdef ad
              ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
          JOIN (pg_class c JOIN pg_namespace nc ON c.relnamespace = nc.oid)
              ON a.attrelid = c.oid
          JOIN (pg_type t JOIN pg_namespace nt ON t.typnamespace = nt.oid)
              ON a.atttypid = t.oid
          LEFT JOIN (pg_type bt JOIN pg_namespace nbt ON bt.typnamespace = nbt.oid)
              ON t.typtype = $15 AND t.typbasetype = bt.oid
          LEFT JOIN (pg_collation co JOIN pg_namespace nco ON co.collnamespace = nco.oid)
              ON a.attcollation = co.oid AND (nco.nspname <> $16::name OR co.collname <> $17::name)
          LEFT JOIN pg_depend dep
              ON dep.refobjid = a.attrelid and dep.refobjsubid = a.attnum and dep.deptype = $18
          LEFT JOIN pg_class seqclass
              ON seqclass.oid = dep.objid
          LEFT JOIN pg_namespace seqsch
              ON seqsch.oid = seqclass.relnamespace
      WHERE
          NOT pg_is_other_temp_schema(nc.oid)
          AND a.attnum > $19
          AND NOT a.attisdropped
          AND c.relkind in ($20, $21, $22, $23, $24)
          AND nc.nspname = ANY($1)
  ),
  columns_agg AS (
    SELECT DISTINCT
        info.table_schema AS table_schema,
        info.table_name AS table_name,
        array_agg(row(
          info.column_name,
          info.description,
          info.is_nullable::boolean,
          info.data_type,
          info.nominal_data_type,
          info.character_maximum_length,
          info.column_default,
          coalesce(enum_info.vals, $25)) order by info.position) as columns
    FROM columns info
    LEFT OUTER JOIN (
        SELECT
            e.enumtypid,
            array_agg(e.enumlabel ORDER BY e.enumsortorder) AS vals
        FROM pg_type t
        JOIN pg_enum e ON t.oid = e.enumtypid
        JOIN pg_namespace n ON n.oid = t.typnamespace
        GROUP BY enumtypid
    ) AS enum_info ON info.base_type = enum_info.enumtypid
    WHERE info.table_schema NOT IN ($26, $27)
    GROUP BY info.table_schema, info.table_name
  ),
  tbl_constraints AS (
      SELECT
          c.conname::name AS constraint_name,
          nr.nspname::name AS table_schema,
          r.relname::name AS table_name
      FROM pg_namespace nc
      JOIN pg_constraint c ON nc.oid = c.connamespace
      JOIN pg_class r ON c.conrelid = r.oid
      JOIN pg_namespace nr ON nr.oid = r.relnamespace
      WHERE
        r.relkind IN ($28, $29)
        AND NOT pg_is_other_temp_schema(nr.oid)
        AND c.contype = $30
  ),
  key_col_usage AS (
      SELECT
          ss.conname::name AS constraint_name,
          ss.nr_nspname::name AS table_schema,
          ss.relname::name AS table_name,
          a.attname::name AS column_name,
          (ss.x).n::integer AS ordinal_position,
          CASE
              WHEN ss.contype = $31 THEN information_schema._pg_index_position(ss.conindid, ss.confkey[(ss.x).n])
              ELSE $32::integer
          END::integer AS position_in_unique_constraint
      FROM pg_attribute a
      JOIN (
        SELECT r.oid AS roid,
          r.relname,
          r.relowner,
          nc.nspname AS nc_nspname,
          nr.nspname AS nr_nspname,
          c.oid AS coid,
          c.conname,
          c.contype,
          c.conindid,
          c.confkey,
          information_schema._pg_expandarray(c.conkey) AS x
        FROM pg_namespace nr
        JOIN pg_class r
          ON nr.oid = r.relnamespace
        JOIN pg_constraint c
          ON r.oid = c.conrelid
        JOIN pg_namespace nc
          ON c.connamespace = nc.oid
        WHERE
          c.contype in ($33, $34)
          AND r.relkind IN ($35, $36)
          AND NOT pg_is_other_temp_schema(nr.oid)
      ) ss ON a.attrelid = ss.roid AND a.attnum = (ss.x).x
      WHERE
        NOT a.attisdropped
  ),
  tbl_pk_cols AS (
    SELECT
        key_col_usage.table_schema,
        key_col_usage.table_name,
        array_agg(key_col_usage.column_name) as pk_cols
    FROM
        tbl_constraints
    JOIN
        key_col_usage
    ON
        key_col_usage.table_name = tbl_constraints.table_name AND
        key_col_usage.table_schema = tbl_constraints.table_schema AND
        key_col_usage.constraint_name = tbl_constraints.constraint_name
    WHERE
        key_col_usage.table_schema NOT IN ($37, $38)
    GROUP BY key_col_usage.table_schema, key_col_usage.table_name
  )
  SELECT
    n.nspname AS table_schema,
    c.relname AS table_name,
    d.description AS table_description,
    c.relkind IN ($39,$40) as is_view,
    (
      c.relkind IN ($41,$42)
      OR (
        c.relkind in ($43,$44)
        -- The function `pg_relation_is_updateable` returns a bitmask where 8
        -- corresponds to `1 << CMD_INSERT` in the PostgreSQL source code, i.e.
        -- it's possible to insert into the relation.
        AND (pg_relation_is_updatable(c.oid::regclass, $45) & $46) = $47
      )
    ) AS insertable,
    (
      c.relkind IN ($48,$49)
      OR (
        c.relkind in ($50,$51)
        -- CMD_UPDATE
        AND (pg_relation_is_updatable(c.oid::regclass, $52) & $53) = $54
      )
    ) AS updatable,
    (
      c.relkind IN ($55,$56)
      OR (
        c.relkind in ($57,$58)
        -- CMD_DELETE
        AND (pg_relation_is_updatable(c.oid::regclass, $59) & $60) = $61
      )
    ) AS deletable,
    coalesce(tpks.pk_cols, $62) as pk_cols,
    coalesce(cols_agg.columns, $63) as columns
  FROM pg_class c
  JOIN pg_namespace n ON n.oid = c.relnamespace
  LEFT JOIN pg_description d on d.objoid = c.oid and d.objsubid = $64
  LEFT JOIN tbl_pk_cols tpks ON n.nspname = tpks.table_schema AND c.relname = tpks.table_name
  LEFT JOIN columns_agg cols_agg ON n.nspname = cols_agg.table_schema AND c.relname = cols_agg.table_name
  WHERE c.relkind IN ($65,$66,$67,$68,$69)
  AND n.nspname NOT IN ($70, $71)  AND not c.relispartition ORDER BY table_schema, table_name -- 创建正确的扣费函数，操作membership-true表
CREATE OR REPLACE FUNCTION deduct_daily_free_quota(
  user_uuid UUID,
  deduct_amount INTEGER,
  access_password TEXT
) RETURNS JSON AS $$
DECLARE
  current_daily_used INTEGER;
  current_daily_quota INTEGER;
  current_reward_used INTEGER;
  current_reward_quota INTEGER;
  last_reset DATE;
  uuid_string TEXT;
  daily_available INTEGER;
  reward_available INTEGER;
  daily_deduct INTEGER := 0;
  reward_deduct INTEGER := 0;
  result JSON;
BEGIN
  -- 密码校验
  IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
    RAISE EXCEPTION 'Invalid access password';
  END IF;
  
  -- 参数验证
  IF user_uuid IS NULL THEN
    RAISE EXCEPTION 'user_uuid cannot be null';
  END IF;
  
  -- 严格的UUID格式验证
  uuid_string := user_uuid::text;
  IF LENGTH(uuid_string) != 36 OR 
     uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid UUID format';
  END IF;
  
  IF deduct_amount IS NULL OR deduct_amount <= 0 THEN
    RAISE EXCEPTION 'deduct_amount must be positive';
  END IF;
  
  -- 防止恶意大量扣除
  IF deduct_amount > 100 THEN
    RAISE EXCEPTION 'deduct_amount too large (max: 100)';
  END IF;
  
  -- 获取当前额度使用情况（从true表查询）
  SELECT daily_free_used, daily_free_quota, reward_used, reward_quota, last_free_reset_date
  INTO current_daily_used, current_daily_quota, current_reward_used, current_reward_quota, last_reset
  FROM "membership-true"
  WHERE user_id = user_uuid AND is_verified = TRUE;
  
  -- 如果用户不存在或未认证
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'message', 'User not found or not verified',
      'used_daily', false,
      'used_reward', false
    );
  END IF;
  
  -- 检查是否需要重置每日额度
  IF last_reset < CURRENT_DATE THEN
    UPDATE "membership-true"
    SET 
      daily_free_used = 0,
      reward_used = 0,  -- 奖励额度也每日重置
      last_free_reset_date = CURRENT_DATE,
      updated_at = NOW()
    WHERE user_id = user_uuid;
    current_daily_used := 0;
    current_reward_used := 0;
  END IF;
  
  -- 计算可用额度
  daily_available := GREATEST(0, current_daily_quota - current_daily_used);
  reward_available := GREATEST(0, current_reward_quota - current_reward_used);
  
  -- 检查总额度是否足够
  IF daily_available + reward_available < deduct_amount THEN
    RETURN json_build_object(
      'success', false,
      'message', 'Insufficient quota',
      'daily_available', daily_available,
      'reward_available', reward_available,
      'used_daily', false,
      'used_reward', false
    );
  END IF;
  
  -- 优先扣除每日免费额度
  IF daily_available > 0 THEN
    daily_deduct := LEAST(daily_available, deduct_amount);
    deduct_amount := deduct_amount - daily_deduct;
  END IF;
  
  -- 如果还有剩余，扣除奖励额度
  IF deduct_amount > 0 AND reward_available > 0 THEN
    reward_deduct := LEAST(reward_available, deduct_amount);
  END IF;
  
  -- 执行扣除操作（操作true表，触发器会自动同步到look表）
  UPDATE "membership-true"
  SET 
    daily_free_used = current_daily_used + daily_deduct,
    reward_used = current_reward_used + reward_deduct,
    updated_at = NOW()
  WHERE user_id = user_uuid;
  
  -- 返回扣费结果
  RETURN json_build_object(
    'success', true,
    'message', 'Deduction successful',
    'daily_deducted', daily_deduct,
    'reward_deducted', reward_deduct,
    'used_daily', daily_deduct > 0,
    'used_reward', reward_deduct > 0
  );
END;
$$ LANGUAGE plpgsql with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- 测试查询用户额度信息函数
SELECT get_user_quota_info(
  $1::UUID,
  $2
) insert into cron.job_run_details (jobid, runid, database, username, command, status) values ($1,$2,$3,$4,$5,$6) -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:35:34.254Z

set local search_path = '' with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- 删除测试函数
DROP FUNCTION IF EXISTS reset_membership_word_count_test() -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:01:35.106Z

set local search_path = '' CREATE INDEX IF NOT EXISTS log_events_ea99cbe5_9f1d_40c9_9e0c_11d095808293_timestamp_brin_idx ON log_events_ea99cbe5_9f1d_40c9_9e0c_11d095808293 USING brin (timestamp) -- 检查cron相关的系统表
SELECT schemaname, tablename FROM pg_tables WHERE schemaname = $1 WITH
    pks_uniques_cols AS (
      SELECT
        connamespace,
        conrelid,
        jsonb_agg(column_info.cols) as cols
      FROM pg_constraint
      JOIN lateral (
        SELECT array_agg(cols.attname order by cols.attnum) as cols
        FROM ( select unnest(conkey) as col) _
        JOIN pg_attribute cols on cols.attrelid = conrelid and cols.attnum = col
      ) column_info ON $1
      WHERE
        contype IN ($2, $3) and
        connamespace::regnamespace::text <> $4
      GROUP BY connamespace, conrelid
    )
    SELECT
      ns1.nspname AS table_schema,
      tab.relname AS table_name,
      ns2.nspname AS foreign_table_schema,
      other.relname AS foreign_table_name,
      (ns1.nspname, tab.relname) = (ns2.nspname, other.relname) AS is_self,
      traint.conname  AS constraint_name,
      column_info.cols_and_fcols,
      (column_info.cols IN (SELECT * FROM jsonb_array_elements(pks_uqs.cols))) AS one_to_one
    FROM pg_constraint traint
    JOIN LATERAL (
      SELECT
        array_agg(row(cols.attname, refs.attname) order by ord) AS cols_and_fcols,
        jsonb_agg(cols.attname order by cols.attnum) AS cols
      FROM unnest(traint.conkey, traint.confkey) WITH ORDINALITY AS _(col, ref, ord)
      JOIN pg_attribute cols ON cols.attrelid = traint.conrelid AND cols.attnum = col
      JOIN pg_attribute refs ON refs.attrelid = traint.confrelid AND refs.attnum = ref
    ) AS column_info ON $5
    JOIN pg_namespace ns1 ON ns1.oid = traint.connamespace
    JOIN pg_class tab ON tab.oid = traint.conrelid
    JOIN pg_class other ON other.oid = traint.confrelid
    JOIN pg_namespace ns2 ON ns2.oid = other.relnamespace
    LEFT JOIN pks_uniques_cols pks_uqs ON pks_uqs.connamespace = traint.connamespace AND pks_uqs.conrelid = traint.conrelid
    WHERE traint.contype = $6
   and traint.conparentid = $7 ORDER BY traint.conrelid, traint.conname with base_table_info as ( select c.oid::int8 as id, nc.nspname as schema, c.relname as name, c.relkind, c.relrowsecurity as rls_enabled, c.relforcerowsecurity as rls_forced, c.relreplident, c.relowner, obj_description(c.oid) as comment from pg_class c join pg_namespace nc on nc.oid = c.relnamespace where c.oid = $1 and not pg_is_other_temp_schema(nc.oid) and ( pg_has_role(c.relowner, $2) or has_table_privilege( c.oid, $3 ) or has_any_column_privilege(c.oid, $4) ) ), table_stats as ( select b.id, case when b.relreplident = $5 then $6 when b.relreplident = $7 then $8 when b.relreplident = $9 then $10 else $11 end as replica_identity, pg_total_relation_size(format($12, b.schema, b.name))::int8 as bytes, pg_size_pretty(pg_total_relation_size(format($13, b.schema, b.name))) as size, pg_stat_get_live_tuples(b.id) as live_rows_estimate, pg_stat_get_dead_tuples(b.id) as dead_rows_estimate from base_table_info b where b.relkind in ($14, $15) ), primary_keys as ( select i.indrelid as table_id, jsonb_agg(jsonb_build_object( $16, n.nspname, $17, c.relname, $18, i.indrelid::int8, $19, a.attname )) as primary_keys from pg_index i join pg_class c on i.indrelid = c.oid join pg_attribute a on (a.attrelid = c.oid and a.attnum = any(i.indkey)) join pg_namespace n on c.relnamespace = n.oid where i.indisprimary group by i.indrelid ), relationships as ( select c.conrelid as source_id, c.confrelid as target_id, jsonb_build_object( $20, c.oid::int8, $21, c.conname, $22, c.confdeltype, $23, c.confupdtype, $24, nsa.nspname, $25, csa.relname, $26, sa.attname, $27, nta.nspname, $28, cta.relname, $29, ta.attname ) as rel_info from pg_constraint c join pg_class csa on c.conrelid = csa.oid join pg_namespace nsa on csa.relnamespace = nsa.oid join pg_attribute sa on (sa.attrelid = c.conrelid and sa.attnum = any(c.conkey)) join pg_class cta on c.confrelid = cta.oid join pg_namespace nta on cta.relnamespace = nta.oid join pg_attribute ta on (ta.attrelid = c.confrelid and ta.attnum = any(c.confkey)) where c.contype = $30 ), columns as ( select a.attrelid as table_id, jsonb_agg(jsonb_build_object( $31, (a.attrelid || $32 || a.attnum), $33, c.oid::int8, $34, nc.nspname, $35, c.relname, $36, a.attnum, $37, a.attname, $38, case when a.atthasdef then pg_get_expr(ad.adbin, ad.adrelid) else $39 end, $40, case when t.typtype = $41 then case when bt.typelem <> $42::oid and bt.typlen = $43 then $44 when nbt.nspname = $45 then format_type(t.typbasetype, $46) else $47 end else case when t.typelem <> $48::oid and t.typlen = $49 then $50 when nt.nspname = $51 then format_type(a.atttypid, $52) else $53 end end, $54, case when t.typtype = $55 then case when nt.nspname <> $56 then concat(nt.nspname, $57, coalesce(bt.typname, t.typname)) else coalesce(bt.typname, t.typname) end else coalesce(bt.typname, t.typname) end, $58, a.attidentity in ($59, $60), $61, case a.attidentity when $62 then $63 when $64 then $65 else $66 end, $67, a.attgenerated in ($68), $69, not (a.attnotnull or t.typtype = $70 and t.typnotnull), $71, ( b.relkind in ($72, $73) or (b.relkind in ($74, $75) and pg_column_is_updatable(b.id, a.attnum, $76)) ), $77, uniques.table_id is not null, $78, check_constraints.definition, $79, col_description(c.oid, a.attnum), $80, coalesce( ( select jsonb_agg(e.enumlabel order by e.enumsortorder) from pg_catalog.pg_enum e where e.enumtypid = coalesce(bt.oid, t.oid) or e.enumtypid = coalesce(bt.typelem, t.typelem) ), $81::jsonb ) ) order by a.attnum) as columns from pg_attribute a join base_table_info b on a.attrelid = b.id join pg_class c on a.attrelid = c.oid join pg_namespace nc on c.relnamespace = nc.oid left join pg_attrdef ad on (a.attrelid = ad.adrelid and a.attnum = ad.adnum) join pg_type t on a.atttypid = t.oid join pg_namespace nt on t.typnamespace = nt.oid left join pg_type bt on (t.typtype = $82 and t.typbasetype = bt.oid) left join pg_namespace nbt on bt.typnamespace = nbt.oid left join ( select conrelid as table_id, conkey[$83] as ordinal_position from pg_catalog.pg_constraint where contype = $84 and cardinality(conkey) = $85 group by conrelid, conkey[1] ) as uniques on uniques.table_id = a.attrelid and uniques.ordinal_position = a.attnum left join ( select distinct on (conrelid, conkey[1]) conrelid as table_id, conkey[$86] as ordinal_position, substring( pg_get_constraintdef(oid, $87), $88, length(pg_get_constraintdef(oid, $89)) - $90 ) as definition from pg_constraint where contype = $91 and cardinality(conkey) = $92 order by conrelid, conkey[1], oid asc ) as check_constraints on check_constraints.table_id = a.attrelid and check_constraints.ordinal_position = a.attnum where a.attnum > $93 and not a.attisdropped group by a.attrelid ) select case b.relkind when $94 then jsonb_build_object( $95, b.relkind, $96, b.id, $97, b.schema, $98, b.name, $99, b.rls_enabled, $100, b.rls_forced, $101, ts.replica_identity, $102, ts.bytes, $103, ts.size, $104, ts.live_rows_estimate, $105, ts.dead_rows_estimate, $106, b.comment, $107, coalesce(pk.primary_keys, $108::jsonb), $109, coalesce( (select jsonb_agg(r.rel_info) from relationships r where r.source_id = b.id or r.target_id = b.id), $110::jsonb ), $111, coalesce(c.columns, $112::jsonb) ) when $113 then jsonb_build_object( $114, b.relkind, $115, b.id, $116, b.schema, $117, b.name, $118, b.rls_enabled, $119, b.rls_forced, $120, ts.replica_identity, $121, ts.bytes, $122, ts.size, $123, ts.live_rows_estimate, $124, ts.dead_rows_estimate, $125, b.comment, $126, coalesce(pk.primary_keys, $127::jsonb), $128, coalesce( (select jsonb_agg(r.rel_info) from relationships r where r.source_id = b.id or r.target_id = b.id), $129::jsonb ), $130, coalesce(c.columns, $131::jsonb) ) when $132 then jsonb_build_object( $133, b.relkind, $134, b.id, $135, b.schema, $136, b.name, $137, (pg_relation_is_updatable(b.id, $138) & $139) = $140, $141, b.comment, $142, coalesce(c.columns, $143::jsonb) ) when $144 then jsonb_build_object( $145, b.relkind, $146, b.id, $147, b.schema, $148, b.name, $149, $150, $151, b.comment, $152, coalesce(c.columns, $153::jsonb) ) when $154 then jsonb_build_object( $155, b.relkind, $156, b.id, $157, b.schema, $158, b.name, $159, b.comment, $160, coalesce(c.columns, $161::jsonb) ) end as entity from base_table_info b left join table_stats ts on b.id = ts.id left join primary_keys pk on b.id = pk.table_id left join columns c on b.id = c.table_id SELECT add_author_daily_quota_for_prompt_usage($1, $2, $3) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r INSERT INTO "_analytics"."log_events_ea99cbe5_9f1d_40c9_9e0c_11d095808293" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60) -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:10:31.401Z

set local search_path = '' -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:48:53.572Z

set local search_path = '' -- 检查重置条件
SELECT 
  email,
  last_free_reset_date::text as reset_date,
  CURRENT_DATE::text as today,
  (last_free_reset_date < CURRENT_DATE) as needs_reset
FROM "membership-true" 
WHERE is_verified = $1 -- 修改扣费相关函数的权限为SECURITY DEFINER，让函数以管理员权限运行

-- 1. 修改 deduct_daily_free_quota 函数
CREATE OR REPLACE FUNCTION public.deduct_daily_free_quota(user_uuid uuid, deduct_amount integer, access_password text)
 RETURNS json
 LANGUAGE plpgsql
 SECURITY DEFINER  -- 以定义者权限运行
AS $function$
DECLARE
  current_daily_used INTEGER;
  current_daily_quota INTEGER;
  current_reward_used INTEGER;
  current_reward_quota INTEGER;
  last_reset DATE;
  uuid_string TEXT;
  daily_available INTEGER;
  reward_available INTEGER;
  daily_deduct INTEGER := 0;
  reward_deduct INTEGER := 0;
  result JSON;
BEGIN
  -- 密码校验
  IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
    RAISE EXCEPTION 'Invalid access password';
  END IF;
  
  -- 参数验证
  IF user_uuid IS NULL THEN
    RAISE EXCEPTION 'user_uuid cannot be null';
  END IF;
  
  -- 严格的UUID格式验证
  uuid_string := user_uuid::text;
  IF LENGTH(uuid_string) != 36 OR 
     uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid UUID format';
  END IF;
  
  IF deduct_amount IS NULL OR deduct_amount <= 0 THEN
    RAISE EXCEPTION 'deduct_amount must be positive';
  END IF;
  
  -- 防止恶意大量扣除
  IF deduct_amount > 100 THEN
    RAISE EXCEPTION 'deduct_amount too large (max: 100)';
  END IF;
  
  -- 获取当前额度使用情况（从true表查询）
  SELECT daily_free_used, daily_free_quota, reward_used, reward_quota, last_free_reset_date
  INTO current_daily_used, current_daily_quota, current_reward_used, current_reward_quota, last_reset
  FROM "membership-true"
  WHERE user_id = user_uuid AND is_verified = TRUE;
  
  -- 如果用户不存在或未认证
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'message', 'User not found or not verified',
      'used_daily', false,
      'used_reward', false
    );
  END IF;
  
  -- 检查是否需要重置每日额度
  IF last_reset < CURRENT_DATE THEN
    UPDATE "membership-true"
    SET 
      daily_free_used = 0,
      reward_used = 0,  -- 奖励额度也每日重置
      last_free_reset_date = CURRENT_DATE,
      updated_at = NOW()
    WHERE user_id = user_uuid;
    current_daily_used := 0;
    current_reward_used := 0;
  END IF;
  
  -- 计算可用额度
  daily_available := GREATEST(0, current_daily_quota - current_daily_used);
  reward_available := GREATEST(0, current_reward_quota - current_reward_used);
  
  -- 检查总额度是否足够
  IF daily_available + reward_available < deduct_amount THEN
    RETURN json_build_object(
      'success', false,
      'message', 'Insufficient quota',
      'daily_available', daily_available,
      'reward_available', reward_available,
      'used_daily', false,
      'used_reward', false
    );
  END IF;
  
  -- 优先扣除每日免费额度
  IF daily_available > 0 THEN
    daily_deduct := LEAST(daily_available, deduct_amount);
    deduct_amount := deduct_amount - daily_deduct;
  END IF;
  
  -- 如果还有剩余，扣除奖励额度
  IF deduct_amount > 0 AND reward_available > 0 THEN
    reward_deduct := LEAST(reward_available, deduct_amount);
  END IF;
  
  -- 执行扣除操作（操作true表，触发器会自动同步到look表）
  UPDATE "membership-true"
  SET 
    daily_free_used = current_daily_used + daily_deduct,
    reward_used = current_reward_used + reward_deduct,
    updated_at = NOW()
  WHERE user_id = user_uuid;
  
  -- 返回扣费结果
  RETURN json_build_object(
    'success', true,
    'message', 'Deduction successful',
    'daily_deducted', daily_deduct,
    'reward_deducted', reward_deduct,
    'used_daily', daily_deduct > 0,
    'used_reward', reward_deduct > 0
  );
END;
$function$ with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r CREATE TYPE pg_temp.tabledefs AS ENUM('PKEY_INTERNAL','PKEY_EXTERNAL','FKEYS_INTERNAL','FKEYS_EXTERNAL','COMMENTS','FKEYS_NONE','INCLUDE_TRIGGERS','NO_TRIGGERS') SELECT word FROM pg_get_keywords() SELECT
  p.oid :: int8 AS id,
  p.pubname AS name,
  p.pubowner::regrole::text AS owner,
  p.pubinsert AS publish_insert,
  p.pubupdate AS publish_update,
  p.pubdelete AS publish_delete,
  p.pubtruncate AS publish_truncate,
  CASE
    WHEN p.puballtables THEN $1
    ELSE pr.tables
  END AS tables
FROM
  pg_catalog.pg_publication AS p
  LEFT JOIN LATERAL (
    SELECT
      COALESCE(
        array_agg(
          json_build_object(
            $2,
            c.oid :: int8,
            $3,
            c.relname,
            $4,
            nc.nspname
          )
        ),
        $5
      ) AS tables
    FROM
      pg_catalog.pg_publication_rel AS pr
      JOIN pg_class AS c ON pr.prrelid = c.oid
      join pg_namespace as nc on c.relnamespace = nc.oid
    WHERE
      pr.prpubid = p.oid
  ) AS pr ON $6 = $7 SELECT b0."id", b0."name", b0."description", b0."token", b0."type", b0."config_encrypted", b0."user_id", b0."metadata", b0."inserted_at", b0."updated_at" FROM "backends" AS b0 INNER JOIN "sources_backends" AS s2 ON s2."backend_id" = b0."id" INNER JOIN "sources" AS s1 ON (s2."source_id" = s1."id") AND (s1."id" = $1) SET client_encoding = 'UTF8' INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12) INSERT INTO "_analytics"."log_events_ea99cbe5_9f1d_40c9_9e0c_11d095808293" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4) update cron.job_run_details set status = $1, return_message = $2 where status in ($3,$4) -- 手动同步look表数据
UPDATE "membership-look" 
SET daily_free_used = $1, updated_at = NOW()
WHERE user_id = $2 with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r SELECT 
  NOW() as current_timestamp,
  CURRENT_DATE as current_date,
  CURRENT_TIME as current_time,
  timezone($1, NOW()) as utc_time,
  timezone($2, NOW()) as shanghai_time SELECT
  e.name,
  n.nspname AS schema,
  e.default_version,
  x.extversion AS installed_version,
  e.comment
FROM
  pg_available_extensions() e(name, default_version, comment)
  LEFT JOIN pg_extension x ON e.name = x.extname
  LEFT JOIN pg_namespace n ON x.extnamespace = n.oid -- 添加注释说明字段用途
COMMENT ON COLUMN "membership-look".reward_quota IS '奖励额度总量（从true表同步）' INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112),($113,$114,$115,$116),($117,$118,$119,$120),($121,$122,$123,$124) -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:12:57.759Z

set local search_path = '' INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112),($113,$114,$115,$116),($117,$118,$119,$120),($121,$122,$123,$124),($125,$126,$127,$128),($129,$130,$131,$132),($133,$134,$135,$136),($137,$138,$139,$140),($141,$142,$143,$144),($145,$146,$147,$148),($149,$150,$151,$152),($153,$154,$155,$156),($157,$158,$159,$160),($161,$162,$163,$164),($165,$166,$167,$168),($169,$170,$171,$172),($173,$174,$175,$176),($177,$178,$179,$180),($181,$182,$183,$184),($185,$186,$187,$188),($189,$190,$191,$192),($193,$194,$195,$196),($197,$198,$199,$200),($201,$202,$203,$204),($205,$206,$207,$208),($209,$210,$211,$212),($213,$214,$215,$216),($217,$218,$219,$220),($221,$222,$223,$224),($225,$226,$227,$228),($229,$230,$231,$232),($233,$234,$235,$236),($237,$238,$239,$240),($241,$242,$243,$244) SELECT 
  con.oid as id, 
  con.conname as constraint_name, 
  con.confdeltype as deletion_action,
  con.confupdtype as update_action,
  rel.oid as source_id,
  nsp.nspname as source_schema, 
  rel.relname as source_table, 
  (
    SELECT 
      array_agg(
        att.attname 
        ORDER BY 
          un.ord
      ) 
    FROM 
      unnest(con.conkey) WITH ORDINALITY un (attnum, ord) 
      INNER JOIN pg_attribute att ON att.attnum = un.attnum 
    WHERE 
      att.attrelid = rel.oid
  ) source_columns, 
  frel.oid as target_id,
  fnsp.nspname as target_schema, 
  frel.relname as target_table, 
  (
    SELECT 
      array_agg(
        att.attname 
        ORDER BY 
          un.ord
      ) 
    FROM 
      unnest(con.confkey) WITH ORDINALITY un (attnum, ord) 
      INNER JOIN pg_attribute att ON att.attnum = un.attnum 
    WHERE 
      att.attrelid = frel.oid
  ) target_columns 
FROM 
  pg_constraint con 
  INNER JOIN pg_class rel ON rel.oid = con.conrelid 
  INNER JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace 
  INNER JOIN pg_class frel ON frel.oid = con.confrelid 
  INNER JOIN pg_namespace fnsp ON fnsp.oid = frel.relnamespace 
WHERE 
  con.contype = $1
  AND nsp.nspname = $2 -- 重置测试用户数据，给他一些每日免费额度
UPDATE "membership-true" 
SET daily_free_used = $1, reward_used = $2, updated_at = NOW()
WHERE user_id = $3 with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r with _base_query as (select * from public."membership-true" order by "membership-true".id asc nulls last limit $1 offset $2)
  select id,case
        when octet_length(email::text) > $3 
        then left(email::text, $4) || $5
        else email::text
      end as email,case
        when octet_length(membership_level::text) > $6 
        then left(membership_level::text, $7) || $8
        else membership_level::text
      end as membership_level,word_count_limit,word_count_used,created_at,updated_at,user_id,is_verified,daily_free_quota,daily_free_used,last_free_reset_date from _base_query INSERT INTO "_analytics"."log_events_ea99cbe5_9f1d_40c9_9e0c_11d095808293" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20) CREATE SCHEMA IF NOT EXISTS supabase_migrations -- 创建新的扣费函数，支持奖励额度机制
CREATE OR REPLACE FUNCTION deduct_daily_free_quota(
  user_uuid UUID,
  deduct_amount INTEGER,
  access_password TEXT
) RETURNS JSON AS $$
DECLARE
  current_daily_used INTEGER;
  current_daily_quota INTEGER;
  current_reward_used INTEGER;
  current_reward_quota INTEGER;
  last_reset DATE;
  uuid_string TEXT;
  daily_available INTEGER;
  reward_available INTEGER;
  daily_deduct INTEGER := 0;
  reward_deduct INTEGER := 0;
  result JSON;
BEGIN
  -- 密码校验
  IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
    RAISE EXCEPTION 'Invalid access password';
  END IF;
  
  -- 参数验证
  IF user_uuid IS NULL THEN
    RAISE EXCEPTION 'user_uuid cannot be null';
  END IF;
  
  -- 严格的UUID格式验证
  uuid_string := user_uuid::text;
  IF LENGTH(uuid_string) != 36 OR 
     uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid UUID format';
  END IF;
  
  IF deduct_amount IS NULL OR deduct_amount <= 0 THEN
    RAISE EXCEPTION 'deduct_amount must be positive';
  END IF;
  
  -- 防止恶意大量扣除
  IF deduct_amount > 100 THEN
    RAISE EXCEPTION 'deduct_amount too large (max: 100)';
  END IF;
  
  -- 获取当前额度使用情况
  SELECT daily_free_used, daily_free_quota, reward_used, reward_quota, last_free_reset_date
  INTO current_daily_used, current_daily_quota, current_reward_used, current_reward_quota, last_reset
  FROM "membership-look"
  WHERE user_id = user_uuid AND is_verified = TRUE;
  
  -- 如果用户不存在或未认证
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'message', 'User not found or not verified',
      'used_daily', false,
      'used_reward', false
    );
  END IF;
  
  -- 检查是否需要重置每日额度
  IF last_reset < CURRENT_DATE THEN
    UPDATE "membership-look"
    SET 
      daily_free_used = 0,
      reward_used = 0,  -- 奖励额度也每日重置
      last_free_reset_date = CURRENT_DATE,
      updated_at = NOW()
    WHERE user_id = user_uuid;
    current_daily_used := 0;
    current_reward_used := 0;
  END IF;
  
  -- 计算可用额度
  daily_available := GREATEST(0, current_daily_quota - current_daily_used);
  reward_available := GREATEST(0, current_reward_quota - current_reward_used);
  
  -- 检查总额度是否足够
  IF daily_available + reward_available < deduct_amount THEN
    RETURN json_build_object(
      'success', false,
      'message', 'Insufficient quota',
      'daily_available', daily_available,
      'reward_available', reward_available,
      'used_daily', false,
      'used_reward', false
    );
  END IF;
  
  -- 优先扣除每日免费额度
  IF daily_available > 0 THEN
    daily_deduct := LEAST(daily_available, deduct_amount);
    deduct_amount := deduct_amount - daily_deduct;
  END IF;
  
  -- 如果还有剩余，扣除奖励额度
  IF deduct_amount > 0 AND reward_available > 0 THEN
    reward_deduct := LEAST(reward_available, deduct_amount);
  END IF;
  
  -- 执行扣除操作
  UPDATE "membership-look"
  SET 
    daily_free_used = current_daily_used + daily_deduct,
    reward_used = current_reward_used + reward_deduct,
    updated_at = NOW()
  WHERE user_id = user_uuid;
  
  -- 返回扣费结果
  RETURN json_build_object(
    'success', true,
    'message', 'Deduction successful',
    'daily_deducted', daily_deduct,
    'reward_deducted', reward_deduct,
    'used_daily', daily_deduct > 0,
    'used_reward', reward_deduct > 0
  );
END;
$$ LANGUAGE plpgsql -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:20:54.895Z

set local search_path = '' SELECT b0."id", b0."name", b0."description", b0."token", b0."type", b0."config_encrypted", b0."user_id", b0."metadata", b0."inserted_at", b0."updated_at" FROM "backends" AS b0 INNER JOIN "sources_backends" AS s2 ON s2."backend_id" = b0."id" INNER JOIN "sources" AS s1 ON (s2."source_id" = s1."id") AND (s1."log_events_updated_at" >= $1::timestamp + (-$2::decimal::numeric * interval $3)) ORDER BY s1."log_events_updated_at" DESC INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20) WITH pgrst_source AS ( SELECT "public"."membership-look"."word_count_used", "public"."membership-look"."word_count_limit", "public"."membership-look"."membership_level", "public"."membership-look"."is_verified", "public"."membership-look"."daily_free_quota", "public"."membership-look"."daily_free_used", "public"."membership-look"."last_free_reset_date" FROM "public"."membership-look"  WHERE  "public"."membership-look"."user_id" = $1    )  SELECT $2::bigint AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, coalesce(json_agg(_postgrest_t)->$3, $4) AS body, nullif(current_setting($5, $6), $7) AS response_headers, nullif(current_setting($8, $9), $10) AS response_status, $11 AS response_inserted FROM ( SELECT * FROM pgrst_source ) _postgrest_t -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:20:40.336Z

set local search_path = '' -- source: dashboard
-- user: self host
-- date: 2025-08-02T09:51:13.247Z

set local search_path = '' -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:14:43.643Z

set local search_path = '' DO $$BEGIN LISTEN "supavisor_local_2_5"; END$$ SELECT p0."id", p0."name", p0."stripe_id", p0."price", p0."period", p0."limit_sources", p0."limit_rate_limit", p0."limit_source_rate_limit", p0."limit_alert_freq", p0."limit_saved_search_limit", p0."limit_team_users_limit", p0."limit_source_fields_limit", p0."limit_source_ttl", p0."type", p0."inserted_at", p0."updated_at" FROM "plans" AS p0 with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- 检查pg_cron相关设置
SHOW shared_preload_libraries SELECT s0."id", s0."name", s0."service_name", s0."token", s0."public_token", s0."favorite", s0."bigquery_table_ttl", s0."api_quota", s0."webhook_notification_url", s0."slack_hook_url", s0."bq_table_partition_type", s0."custom_event_message_keys", s0."log_events_updated_at", s0."notifications_every", s0."lock_schema", s0."validate_schema", s0."drop_lql_filters", s0."drop_lql_string", s0."v2_pipeline", s0."disable_tailing", s0."suggested_keys", s0."transform_copy_fields", s0."user_id", s0."notifications", s0."inserted_at", s0."updated_at" FROM "sources" AS s0 WHERE (s0."log_events_updated_at" > $1::timestamp + (-$2::decimal::numeric * interval $3)) ORDER BY s0."log_events_updated_at" LIMIT $4 -- 创建修改版的作者奖励函数，只在使用每日免费额度时添加奖励（操作true表）
CREATE OR REPLACE FUNCTION add_author_daily_quota_for_prompt_usage(
  user_uuid_param UUID,
  prompt_id_param UUID,
  access_password TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  author_user_id UUID;
  uuid_string TEXT;
  prompt_uuid_string TEXT;
BEGIN
  -- 密码校验
  IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
    RAISE EXCEPTION 'Invalid access password';
  END IF;
  
  -- 参数验证
  IF user_uuid_param IS NULL THEN
    RAISE EXCEPTION 'user_uuid_param cannot be null';
  END IF;
  
  IF prompt_id_param IS NULL THEN
    RAISE EXCEPTION 'prompt_id_param cannot be null';
  END IF;
  
  -- UUID格式验证
  uuid_string := user_uuid_param::text;
  IF LENGTH(uuid_string) != 36 OR 
     uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid user UUID format';
  END IF;
  
  prompt_uuid_string := prompt_id_param::text;
  IF LENGTH(prompt_uuid_string) != 36 OR 
     prompt_uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid prompt UUID format';
  END IF;
  
  -- 获取提示词作者的user_id
  SELECT created_by INTO author_user_id
  FROM "propmt-zhanshi"
  WHERE id = prompt_id_param;
  
  -- 如果提示词不存在
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- 如果是作者自己使用自己的提示词，不给奖励
  IF author_user_id = user_uuid_param THEN
    RETURN FALSE;
  END IF;
  
  -- 为作者添加1次奖励额度（操作true表，触发器会自动同步到look表）
  UPDATE "membership-true"
  SET 
    reward_quota = COALESCE(reward_quota, 0) + 1,
    updated_at = NOW()
  WHERE user_id = author_user_id AND is_verified = TRUE;
  
  -- 检查是否更新成功
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql INSERT INTO "_analytics"."log_events_db2f88a6_2f21_4309_8baf_6ff0b23b88dc" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:10:09.618Z

set local search_path = '' SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE tablename IN ($1, $2) select
    auth.rolname,
    statements.query,
    statements.calls,
    -- -- Postgres 13, 14, 15
    statements.total_exec_time + statements.total_plan_time as total_time,
    statements.min_exec_time + statements.min_plan_time as min_time,
    statements.max_exec_time + statements.max_plan_time as max_time,
    statements.mean_exec_time + statements.mean_plan_time as mean_time,
    -- -- Postgres <= 12
    -- total_time,
    -- min_time,
    -- max_time,
    -- mean_time,
    statements.rows / statements.calls as avg_rows
  from pg_stat_statements as statements
    inner join pg_authid as auth on statements.userid = auth.oid
  
  order by max_time desc
  limit $1 with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r select (select count(*) from public."membership-true"), $1 as is_estimate SELECT * FROM "refresh_tokens" WHERE token = $1 LIMIT $2 FOR UPDATE SKIP LOCKED -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:25:32.137Z

set local search_path = '' INSERT INTO "refresh_tokens" ("created_at", "instance_id", "parent", "revoked", "session_id", "token", "updated_at", "user_id") VALUES ($1, $2, $3, $4, $5, $6, $7, $8) returning id -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:20:56.467Z

set local search_path = '' SHOW default_table_access_method CREATE TABLE IF NOT EXISTS log_events_ea99cbe5_9f1d_40c9_9e0c_11d095808293 (
  id TEXT PRIMARY KEY,
  body JSONB,
  event_message TEXT,
  timestamp TIMESTAMP
) with tables as (SELECT
  c.oid :: int8 AS id,
  nc.nspname AS schema,
  c.relname AS name,
  c.relrowsecurity AS rls_enabled,
  c.relforcerowsecurity AS rls_forced,
  CASE
    WHEN c.relreplident = $1 THEN $2
    WHEN c.relreplident = $3 THEN $4
    WHEN c.relreplident = $5 THEN $6
    ELSE $7
  END AS replica_identity,
  pg_total_relation_size(format($8, nc.nspname, c.relname)) :: int8 AS bytes,
  pg_size_pretty(
    pg_total_relation_size(format($9, nc.nspname, c.relname))
  ) AS size,
  pg_stat_get_live_tuples(c.oid) AS live_rows_estimate,
  pg_stat_get_dead_tuples(c.oid) AS dead_rows_estimate,
  obj_description(c.oid) AS comment,
  coalesce(pk.primary_keys, $10) as primary_keys,
  coalesce(
    jsonb_agg(relationships) filter (where relationships is not null),
    $11
  ) as relationships
FROM
  pg_namespace nc
  JOIN pg_class c ON nc.oid = c.relnamespace
  left join (
    select
      table_id,
      jsonb_agg(_pk.*) as primary_keys
    from (
      select
        n.nspname as schema,
        c.relname as table_name,
        a.attname as name,
        c.oid :: int8 as table_id
      from
        pg_index i,
        pg_class c,
        pg_attribute a,
        pg_namespace n
      where
        i.indrelid = c.oid
        and c.relnamespace = n.oid
        and a.attrelid = c.oid
        and a.attnum = any (i.indkey)
        and i.indisprimary
    ) as _pk
    group by table_id
  ) as pk
  on pk.table_id = c.oid
  left join (
    select
      c.oid :: int8 as id,
      c.conname as constraint_name,
      nsa.nspname as source_schema,
      csa.relname as source_table_name,
      sa.attname as source_column_name,
      nta.nspname as target_table_schema,
      cta.relname as target_table_name,
      ta.attname as target_column_name
    from
      pg_constraint c
    join (
      pg_attribute sa
      join pg_class csa on sa.attrelid = csa.oid
      join pg_namespace nsa on csa.relnamespace = nsa.oid
    ) on sa.attrelid = c.conrelid and sa.attnum = any (c.conkey)
    join (
      pg_attribute ta
      join pg_class cta on ta.attrelid = cta.oid
      join pg_namespace nta on cta.relnamespace = nta.oid
    ) on ta.attrelid = c.confrelid and ta.attnum = any (c.confkey)
    where
      c.contype = $12
  ) as relationships
  on (relationships.source_schema = nc.nspname and relationships.source_table_name = c.relname)
  or (relationships.target_table_schema = nc.nspname and relationships.target_table_name = c.relname)
WHERE
  c.relkind IN ($13, $14)
  AND NOT pg_is_other_temp_schema(nc.oid)
  AND (
    pg_has_role(c.relowner, $15)
    OR has_table_privilege(
      c.oid,
      $16
    )
    OR has_any_column_privilege(c.oid, $17)
  )
group by
  c.oid,
  c.relname,
  c.relrowsecurity,
  c.relforcerowsecurity,
  c.relreplident,
  nc.nspname,
  pk.primary_keys
)
  , columns as (-- Adapted from information_schema.columns

SELECT
  c.oid :: int8 AS table_id,
  nc.nspname AS schema,
  c.relname AS table,
  (c.oid || $18 || a.attnum) AS id,
  a.attnum AS ordinal_position,
  a.attname AS name,
  CASE
    WHEN a.atthasdef THEN pg_get_expr(ad.adbin, ad.adrelid)
    ELSE $19
  END AS default_value,
  CASE
    WHEN t.typtype = $20 THEN CASE
      WHEN bt.typelem <> $21 :: oid
      AND bt.typlen = $22 THEN $23
      WHEN nbt.nspname = $24 THEN format_type(t.typbasetype, $25)
      ELSE $26
    END
    ELSE CASE
      WHEN t.typelem <> $27 :: oid
      AND t.typlen = $28 THEN $29
      WHEN nt.nspname = $30 THEN format_type(a.atttypid, $31)
      ELSE $32
    END
  END AS data_type,
  COALESCE(bt.typname, t.typname) AS format,
  a.attidentity IN ($33, $34) AS is_identity,
  CASE
    a.attidentity
    WHEN $35 THEN $36
    WHEN $37 THEN $38
    ELSE $39
  END AS identity_generation,
  a.attgenerated IN ($40) AS is_generated,
  NOT (
    a.attnotnull
    OR t.typtype = $41 AND t.typnotnull
  ) AS is_nullable,
  (
    c.relkind IN ($42, $43)
    OR c.relkind IN ($44, $45) AND pg_column_is_updatable(c.oid, a.attnum, $46)
  ) AS is_updatable,
  uniques.table_id IS NOT NULL AS is_unique,
  check_constraints.definition AS "check",
  array_to_json(
    array(
      SELECT
        enumlabel
      FROM
        pg_catalog.pg_enum enums
      WHERE
        enums.enumtypid = coalesce(bt.oid, t.oid)
        OR enums.enumtypid = coalesce(bt.typelem, t.typelem)
      ORDER BY
        enums.enumsortorder
    )
  ) AS enums,
  col_description(c.oid, a.attnum) AS comment
FROM
  pg_attribute a
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid
  AND a.attnum = ad.adnum
  JOIN (
    pg_class c
    JOIN pg_namespace nc ON c.relnamespace = nc.oid
  ) ON a.attrelid = c.oid
  JOIN (
    pg_type t
    JOIN pg_namespace nt ON t.typnamespace = nt.oid
  ) ON a.atttypid = t.oid
  LEFT JOIN (
    pg_type bt
    JOIN pg_namespace nbt ON bt.typnamespace = nbt.oid
  ) ON t.typtype = $47
  AND t.typbasetype = bt.oid
  LEFT JOIN (
    SELECT DISTINCT ON (table_id, ordinal_position)
      conrelid AS table_id,
      conkey[$48] AS ordinal_position
    FROM pg_catalog.pg_constraint
    WHERE contype = $49 AND cardinality(conkey) = $50
  ) AS uniques ON uniques.table_id = c.oid AND uniques.ordinal_position = a.attnum
  LEFT JOIN (
    -- We only select the first column check
    SELECT DISTINCT ON (table_id, ordinal_position)
      conrelid AS table_id,
      conkey[$51] AS ordinal_position,
      substring(
        pg_get_constraintdef(pg_constraint.oid, $52),
        $53,
        length(pg_get_constraintdef(pg_constraint.oid, $54)) - $55
      ) AS "definition"
    FROM pg_constraint
    WHERE contype = $56 AND cardinality(conkey) = $57
    ORDER BY table_id, ordinal_position, oid asc
  ) AS check_constraints ON check_constraints.table_id = c.oid AND check_constraints.ordinal_position = a.attnum
WHERE
  NOT pg_is_other_temp_schema(nc.oid)
  AND a.attnum > $58
  AND NOT a.attisdropped
  AND (c.relkind IN ($59, $60, $61, $62, $63))
  AND (
    pg_has_role(c.relowner, $64)
    OR has_column_privilege(
      c.oid,
      a.attnum,
      $65
    )
  )
)
select
  *
  , 
COALESCE(
  (
    SELECT
      array_agg(row_to_json(columns)) FILTER (WHERE columns.table_id = tables.id)
    FROM
      columns
  ),
  $66
) AS columns
from tables where schema IN ($67) SET search_path TO _realtime with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r SELECT mfa_factors.created_at, mfa_factors.factor_type, mfa_factors.friendly_name, mfa_factors.id, mfa_factors.last_challenged_at, mfa_factors.phone, mfa_factors.secret, mfa_factors.status, mfa_factors.updated_at, mfa_factors.user_id, mfa_factors.web_authn_aaguid, mfa_factors.web_authn_credential FROM mfa_factors AS mfa_factors WHERE user_id = $1 with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r set local schema '' SELECT NOW() AT TIME ZONE $1 as current_utc_time SELECT identities.created_at, identities.email, identities.id, identities.identity_data, identities.last_sign_in_at, identities.provider, identities.provider_id, identities.updated_at, identities.user_id FROM identities AS identities WHERE user_id = $1 SELECT s0."version" FROM "schema_migrations" AS s0 UPDATE "sessions" AS sessions SET "ip" = $1, "refreshed_at" = $2, "updated_at" = $3, "user_agent" = $4 WHERE sessions.id = $5 with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r COMMENT ON COLUMN "membership-true".reward_used IS '奖励额度已使用量' -- 3. 修改 add_author_daily_quota_for_prompt_usage 函数
CREATE OR REPLACE FUNCTION public.add_author_daily_quota_for_prompt_usage(user_uuid_param uuid, prompt_id_param uuid, access_password text)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER  -- 以定义者权限运行
AS $function$
DECLARE
  author_user_id UUID;
  uuid_string TEXT;
  prompt_uuid_string TEXT;
BEGIN
  -- 密码校验
  IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
    RAISE EXCEPTION 'Invalid access password';
  END IF;
  
  -- 参数验证
  IF user_uuid_param IS NULL THEN
    RAISE EXCEPTION 'user_uuid_param cannot be null';
  END IF;
  
  IF prompt_id_param IS NULL THEN
    RAISE EXCEPTION 'prompt_id_param cannot be null';
  END IF;
  
  -- UUID格式验证
  uuid_string := user_uuid_param::text;
  IF LENGTH(uuid_string) != 36 OR 
     uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid user UUID format';
  END IF;
  
  prompt_uuid_string := prompt_id_param::text;
  IF LENGTH(prompt_uuid_string) != 36 OR 
     prompt_uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid prompt UUID format';
  END IF;
  
  -- 获取提示词作者的user_id
  SELECT created_by INTO author_user_id
  FROM "propmt-zhanshi"
  WHERE id = prompt_id_param;
  
  -- 如果提示词不存在
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- 如果是作者自己使用自己的提示词，不给奖励
  IF author_user_id = user_uuid_param THEN
    RETURN FALSE;
  END IF;
  
  -- 为作者添加1次奖励额度（操作true表，触发器会自动同步到look表）
  UPDATE "membership-true"
  SET 
    reward_quota = COALESCE(reward_quota, 0) + 1,
    updated_at = NOW()
  WHERE user_id = author_user_id AND is_verified = TRUE;
  
  -- 检查是否更新成功
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$function$ CREATE TABLE IF NOT EXISTS log_events_f48b8b2f_d050_4c12_ac91_57db3240fa88 (
  id TEXT PRIMARY KEY,
  body JSONB,
  event_message TEXT,
  timestamp TIMESTAMP
) begin INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76) INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100) SELECT s0."version" FROM "realtime"."schema_migrations" AS s0 -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:21:21.482Z

set local search_path = '' WITH pgrst_source AS ( SELECT "public"."membership-look".* FROM "public"."membership-look"  WHERE  "public"."membership-look"."user_id" = $1    )  SELECT $2::bigint AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, coalesce(json_agg(_postgrest_t)->$3, $4) AS body, nullif(current_setting($5, $6), $7) AS response_headers, nullif(current_setting($8, $9), $10) AS response_status, $11 AS response_inserted FROM ( SELECT * FROM pgrst_source ) _postgrest_t select set_config('search_path', $1, true), set_config($2, $3, true), set_config('role', $4, true), set_config('request.jwt.claims', $5, true), set_config('request.method', $6, true), set_config('request.path', $7, true), set_config('request.headers', $8, true), set_config('request.cookies', $9, true), set_config($10, $11, true), set_config($12, $13, true) BEGIN TRANSACTION READ ONLY WITH pgrst_source AS (SELECT pgrst_call.pgrst_scalar FROM (SELECT $1 AS json_data) pgrst_payload, LATERAL (SELECT "user_uuid_param", "prompt_id_param", "access_password" FROM json_to_record(pgrst_payload.json_data) AS _("user_uuid_param" uuid, "prompt_id_param" uuid, "access_password" text) LIMIT $2) pgrst_body , LATERAL (SELECT "public"."add_author_daily_quota_for_prompt_usage"("user_uuid_param" := pgrst_body."user_uuid_param", "prompt_id_param" := pgrst_body."prompt_id_param", "access_password" := pgrst_body."access_password") pgrst_scalar) pgrst_call) SELECT $3::bigint AS total_result_set, $4 AS page_total, coalesce(json_agg(_postgrest_t.pgrst_scalar)->$5, $6) AS body, nullif(current_setting($7, $8), $9) AS response_headers, nullif(current_setting($10, $11), $12) AS response_status, $13 AS response_inserted FROM (SELECT "add_author_daily_quota_for_prompt_usage".* FROM "pgrst_source" AS "add_author_daily_quota_for_prompt_usage"     ) _postgrest_t -- source: dashboard
-- user: self host
-- date: 2025-08-02T09:53:31.221Z

set local search_path = '' -- 修复 reset_daily_free_quota 函数，添加奖励额度重置功能
-- 让手动批量重置与自动重置行为保持一致

CREATE OR REPLACE FUNCTION public.reset_daily_free_quota()
 RETURNS integer
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  reset_count INTEGER := 0;
BEGIN
  -- 重置所有认证用户的每日免费额度和奖励额度
  UPDATE "membership-true"
  SET 
    daily_free_used = 0,
    reward_used = 0,  -- 添加奖励额度重置
    last_free_reset_date = CURRENT_DATE,
    updated_at = NOW()
  WHERE 
    is_verified = TRUE 
    AND last_free_reset_date < CURRENT_DATE;
  
  GET DIAGNOSTICS reset_count = ROW_COUNT;
  RETURN reset_count;
END;
$function$ select *, coalesce((select array_agg(distinct i.provider) from auth.identities i where i.user_id = auth.users.id), $1::text[]) as providers from auth.users order by "created_at" desc nulls last limit $2 offset $3 SELECT 
  email,
  last_free_reset_date,
  last_free_reset_date::text as reset_date_text,
  CURRENT_DATE::text as today_text,
  (last_free_reset_date < CURRENT_DATE) as needs_reset
FROM "membership-true" 
WHERE user_id = $1 INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4) select
      s.oid as "id",
      w.fdwname as "name",
      s.srvname as "server_name",
      s.srvoptions as "server_options",
      c.proname as "handler",
      (
        select jsonb_agg(
          jsonb_build_object(
            $1, c.oid::bigint,
            $2, relnamespace::regnamespace::text,
            $3, c.relname,
            $4, (
              select jsonb_agg(
                jsonb_build_object(
                  $5, a.attname,
                  $6, pg_catalog.format_type(a.atttypid, a.atttypmod)
                )
              )
              from pg_catalog.pg_attribute a
              where a.attrelid = c.oid and a.attnum > $7 and not a.attisdropped
            ),
            $8, t.ftoptions
          )
        )
        from pg_catalog.pg_class c
        join pg_catalog.pg_foreign_table t on c.oid = t.ftrelid
        where c.oid = any (select t.ftrelid from pg_catalog.pg_foreign_table t where t.ftserver = s.oid)
      ) as "tables"
    from pg_catalog.pg_foreign_server s
    join pg_catalog.pg_foreign_data_wrapper w on s.srvfdw = w.oid
    join pg_catalog.pg_proc c on w.fdwhandler = c.oid INSERT INTO "_analytics"."log_events_d880e866_2184_4823_8a8c_f3af54ad1bfd" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28) INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112),($113,$114,$115,$116) -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:29:55.751Z

set local search_path = '' SELECT 
  p.proname,
  p.prosecdef,
  CASE WHEN p.prosecdef THEN $1 ELSE $2 END as security_type
FROM pg_proc p
WHERE p.proname IN ($3, $4, $5) -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:45:38.805Z

set local search_path = '' update public."membership-true" set (daily_free_quota) = (select daily_free_quota from json_populate_record($1::public."membership-true", $2)) where id = $3 -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:10:41.382Z

set local search_path = '' SELECT 
    NOW() AT TIME ZONE $1 as current_utc_time,
    $2::timestamp AT TIME ZONE $3 as scheduled_time,
    CASE 
        WHEN NOW() AT TIME ZONE $4 < $5::timestamp AT TIME ZONE $6 
        THEN $7
        ELSE $8
    END as task_status -- 检查cron后台工作进程设置
SHOW cron.max_running_jobs NOTIFY supavisor_local_2_5, 'supavisor@ea069aedc197' SELECT users.aud, users.banned_until, users.confirmation_sent_at, users.confirmation_token, users.confirmed_at, users.created_at, users.deleted_at, users.email, users.email_change, users.email_change_confirm_status, users.email_change_sent_at, users.email_change_token_current, users.email_change_token_new, users.email_confirmed_at, users.encrypted_password, users.id, users.instance_id, users.invited_at, users.is_anonymous, users.is_sso_user, users.last_sign_in_at, users.phone, users.phone_change, users.phone_change_sent_at, users.phone_change_token, users.phone_confirmed_at, users.raw_app_meta_data, users.raw_user_meta_data, users.reauthentication_sent_at, users.reauthentication_token, users.recovery_sent_at, users.recovery_token, users.role, users.updated_at FROM users AS users WHERE instance_id = $1 and id = $2 LIMIT $3 -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:48:00.927Z

set local search_path = '' SELECT
  pol.oid :: int8 AS id,
  n.nspname AS schema,
  c.relname AS table,
  c.oid :: int8 AS table_id,
  pol.polname AS name,
  CASE
    WHEN pol.polpermissive THEN $1 :: text
    ELSE $2 :: text
  END AS action,
  CASE
    WHEN pol.polroles = $3 :: oid [] THEN array_to_json(
      string_to_array($4 :: text, $5 :: text) :: name []
    )
    ELSE array_to_json(
      ARRAY(
        SELECT
          pg_roles.rolname
        FROM
          pg_roles
        WHERE
          pg_roles.oid = ANY (pol.polroles)
        ORDER BY
          pg_roles.rolname
      )
    )
  END AS roles,
  CASE
    pol.polcmd
    WHEN $6 :: "char" THEN $7 :: text
    WHEN $8 :: "char" THEN $9 :: text
    WHEN $10 :: "char" THEN $11 :: text
    WHEN $12 :: "char" THEN $13 :: text
    WHEN $14 :: "char" THEN $15 :: text
    ELSE $16 :: text
  END AS command,
  pg_get_expr(pol.polqual, pol.polrelid) AS definition,
  pg_get_expr(pol.polwithcheck, pol.polrelid) AS check
FROM
  pg_policy pol
  JOIN pg_class c ON c.oid = pol.polrelid
  LEFT JOIN pg_namespace n ON n.oid = c.relnamespace
 WHERE n.nspname NOT IN ($17,$18,$19) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- 查看membership-look表的数据结构和内容
SELECT user_id, email, is_verified, daily_free_quota, daily_free_used, reward_quota, reward_used, last_free_reset_date 
FROM "membership-look" 
LIMIT $1 -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:06:00.142Z

set local search_path = '' -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:13:14.350Z

set local search_path = '' SET search_path TO storage,public,extensions SELECT u0."id", u0."email", u0."provider", u0."token", u0."api_key", u0."old_api_key", u0."email_preferred", u0."name", u0."image", u0."email_me_product", u0."admin", u0."phone", u0."bigquery_project_id", u0."bigquery_dataset_location", u0."bigquery_dataset_id", u0."bigquery_udfs_hash", u0."bigquery_processed_bytes_limit", u0."api_quota", u0."valid_google_account", u0."provider_uid", u0."company", u0."billing_enabled", u0."endpoints_beta", u0."metadata", u0."preferences", u0."partner_upgraded", u0."partner_id", u0."inserted_at", u0."updated_at" FROM "users" AS u0 WHERE (u0."api_key" = $1) SELECT id, email, word_count_used, updated_at FROM "membership-true" ORDER BY updated_at DESC SELECT sessions.aal, sessions.created_at, sessions.factor_id, sessions.id, sessions.ip, sessions.not_after, sessions.refreshed_at, sessions.tag, sessions.updated_at, sessions.user_agent, sessions.user_id FROM sessions AS sessions WHERE id = $1 LIMIT $2 update public."membership-true" set (reward_used) = (select reward_used from json_populate_record($1::public."membership-true", $2)) where id = $3 -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:27:58.745Z

set local search_path = '' SELECT
          set_config('role', $1, true),
          set_config('request.jwt.claim.role', $2, true),
          set_config('request.jwt', $3, true),
          set_config('request.jwt.claim.sub', $4, true),
          set_config('request.jwt.claims', $5, true),
          set_config('request.headers', $6, true),
          set_config('request.method', $7, true),
          set_config('request.path', $8, true),
          set_config('storage.operation', $9, true) SELECT s0."id", s0."all_logs_logged", s0."node", s0."inserted_at", s0."updated_at" FROM "system_metrics" AS s0 -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:32:14.250Z

set local search_path = '' SELECT pg_advisory_unlock($1) -- 检查扣费后的数据同步
SELECT $1 as table_name, user_id, daily_free_used, reward_used 
FROM "membership-true" 
WHERE user_id = $2
UNION ALL
SELECT $3 as table_name, user_id, daily_free_used, reward_used 
FROM "membership-look" 
WHERE user_id = $4 with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r CREATE INDEX IF NOT EXISTS log_events_f48b8b2f_d050_4c12_ac91_57db3240fa88_timestamp_brin_idx ON log_events_f48b8b2f_d050_4c12_ac91_57db3240fa88 USING brin (timestamp) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r with
      tables as (SELECT
  c.oid :: int8 AS id,
  nc.nspname AS schema,
  c.relname AS name,
  c.relrowsecurity AS rls_enabled,
  c.relforcerowsecurity AS rls_forced,
  CASE
    WHEN c.relreplident = $1 THEN $2
    WHEN c.relreplident = $3 THEN $4
    WHEN c.relreplident = $5 THEN $6
    ELSE $7
  END AS replica_identity,
  pg_total_relation_size(format($8, nc.nspname, c.relname)) :: int8 AS bytes,
  pg_size_pretty(
    pg_total_relation_size(format($9, nc.nspname, c.relname))
  ) AS size,
  pg_stat_get_live_tuples(c.oid) AS live_rows_estimate,
  pg_stat_get_dead_tuples(c.oid) AS dead_rows_estimate,
  obj_description(c.oid) AS comment,
  coalesce(pk.primary_keys, $10) as primary_keys,
  coalesce(
    jsonb_agg(relationships) filter (where relationships is not null),
    $11
  ) as relationships
FROM
  pg_namespace nc
  JOIN pg_class c ON nc.oid = c.relnamespace
  left join (
    select
      table_id,
      jsonb_agg(_pk.*) as primary_keys
    from (
      select
        n.nspname as schema,
        c.relname as table_name,
        a.attname as name,
        c.oid :: int8 as table_id
      from
        pg_index i,
        pg_class c,
        pg_attribute a,
        pg_namespace n
      where
        i.indrelid = c.oid
        and c.relnamespace = n.oid
        and a.attrelid = c.oid
        and a.attnum = any (i.indkey)
        and i.indisprimary
    ) as _pk
    group by table_id
  ) as pk
  on pk.table_id = c.oid
  left join (
    select
      c.oid :: int8 as id,
      c.conname as constraint_name,
      nsa.nspname as source_schema,
      csa.relname as source_table_name,
      sa.attname as source_column_name,
      nta.nspname as target_table_schema,
      cta.relname as target_table_name,
      ta.attname as target_column_name
    from
      pg_constraint c
    join (
      pg_attribute sa
      join pg_class csa on sa.attrelid = csa.oid
      join pg_namespace nsa on csa.relnamespace = nsa.oid
    ) on sa.attrelid = c.conrelid and sa.attnum = any (c.conkey)
    join (
      pg_attribute ta
      join pg_class cta on ta.attrelid = cta.oid
      join pg_namespace nta on cta.relnamespace = nta.oid
    ) on ta.attrelid = c.confrelid and ta.attnum = any (c.confkey)
    where
      c.contype = $12
  ) as relationships
  on (relationships.source_schema = nc.nspname and relationships.source_table_name = c.relname)
  or (relationships.target_table_schema = nc.nspname and relationships.target_table_name = c.relname)
WHERE
  c.relkind IN ($13, $14)
  AND NOT pg_is_other_temp_schema(nc.oid)
  AND (
    pg_has_role(c.relowner, $15)
    OR has_table_privilege(
      c.oid,
      $16
    )
    OR has_any_column_privilege(c.oid, $17)
  )
group by
  c.oid,
  c.relname,
  c.relrowsecurity,
  c.relforcerowsecurity,
  c.relreplident,
  nc.nspname,
  pk.primary_keys
),
      columns as (-- Adapted from information_schema.columns

SELECT
  c.oid :: int8 AS table_id,
  nc.nspname AS schema,
  c.relname AS table,
  (c.oid || $18 || a.attnum) AS id,
  a.attnum AS ordinal_position,
  a.attname AS name,
  CASE
    WHEN a.atthasdef THEN pg_get_expr(ad.adbin, ad.adrelid)
    ELSE $19
  END AS default_value,
  CASE
    WHEN t.typtype = $20 THEN CASE
      WHEN bt.typelem <> $21 :: oid
      AND bt.typlen = $22 THEN $23
      WHEN nbt.nspname = $24 THEN format_type(t.typbasetype, $25)
      ELSE $26
    END
    ELSE CASE
      WHEN t.typelem <> $27 :: oid
      AND t.typlen = $28 THEN $29
      WHEN nt.nspname = $30 THEN format_type(a.atttypid, $31)
      ELSE $32
    END
  END AS data_type,
  COALESCE(bt.typname, t.typname) AS format,
  a.attidentity IN ($33, $34) AS is_identity,
  CASE
    a.attidentity
    WHEN $35 THEN $36
    WHEN $37 THEN $38
    ELSE $39
  END AS identity_generation,
  a.attgenerated IN ($40) AS is_generated,
  NOT (
    a.attnotnull
    OR t.typtype = $41 AND t.typnotnull
  ) AS is_nullable,
  (
    c.relkind IN ($42, $43)
    OR c.relkind IN ($44, $45) AND pg_column_is_updatable(c.oid, a.attnum, $46)
  ) AS is_updatable,
  uniques.table_id IS NOT NULL AS is_unique,
  check_constraints.definition AS "check",
  array_to_json(
    array(
      SELECT
        enumlabel
      FROM
        pg_catalog.pg_enum enums
      WHERE
        enums.enumtypid = coalesce(bt.oid, t.oid)
        OR enums.enumtypid = coalesce(bt.typelem, t.typelem)
      ORDER BY
        enums.enumsortorder
    )
  ) AS enums,
  col_description(c.oid, a.attnum) AS comment
FROM
  pg_attribute a
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid
  AND a.attnum = ad.adnum
  JOIN (
    pg_class c
    JOIN pg_namespace nc ON c.relnamespace = nc.oid
  ) ON a.attrelid = c.oid
  JOIN (
    pg_type t
    JOIN pg_namespace nt ON t.typnamespace = nt.oid
  ) ON a.atttypid = t.oid
  LEFT JOIN (
    pg_type bt
    JOIN pg_namespace nbt ON bt.typnamespace = nbt.oid
  ) ON t.typtype = $47
  AND t.typbasetype = bt.oid
  LEFT JOIN (
    SELECT DISTINCT ON (table_id, ordinal_position)
      conrelid AS table_id,
      conkey[$48] AS ordinal_position
    FROM pg_catalog.pg_constraint
    WHERE contype = $49 AND cardinality(conkey) = $50
  ) AS uniques ON uniques.table_id = c.oid AND uniques.ordinal_position = a.attnum
  LEFT JOIN (
    -- We only select the first column check
    SELECT DISTINCT ON (table_id, ordinal_position)
      conrelid AS table_id,
      conkey[$51] AS ordinal_position,
      substring(
        pg_get_constraintdef(pg_constraint.oid, $52),
        $53,
        length(pg_get_constraintdef(pg_constraint.oid, $54)) - $55
      ) AS "definition"
    FROM pg_constraint
    WHERE contype = $56 AND cardinality(conkey) = $57
    ORDER BY table_id, ordinal_position, oid asc
  ) AS check_constraints ON check_constraints.table_id = c.oid AND check_constraints.ordinal_position = a.attnum
WHERE
  NOT pg_is_other_temp_schema(nc.oid)
  AND a.attnum > $58
  AND NOT a.attisdropped
  AND (c.relkind IN ($59, $60, $61, $62, $63))
  AND (
    pg_has_role(c.relowner, $64)
    OR has_column_privilege(
      c.oid,
      a.attnum,
      $65
    )
  )
)
    select
      *,
      COALESCE(
  (
    SELECT
      array_agg(row_to_json(columns)) FILTER (WHERE columns.table_id = tables.id)
    FROM
      columns
  ),
  $66
) AS columns
    from tables
where schema in ($67) INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112),($113,$114,$115,$116),($117,$118,$119,$120),($121,$122,$123,$124),($125,$126,$127,$128),($129,$130,$131,$132),($133,$134,$135,$136),($137,$138,$139,$140),($141,$142,$143,$144),($145,$146,$147,$148),($149,$150,$151,$152),($153,$154,$155,$156),($157,$158,$159,$160),($161,$162,$163,$164),($165,$166,$167,$168),($169,$170,$171,$172),($173,$174,$175,$176),($177,$178,$179,$180),($181,$182,$183,$184),($185,$186,$187,$188),($189,$190,$191,$192),($193,$194,$195,$196),($197,$198,$199,$200),($201,$202,$203,$204),($205,$206,$207,$208),($209,$210,$211,$212),($213,$214,$215,$216) SELECT s0."id", s0."name", s0."service_name", s0."token", s0."public_token", s0."favorite", s0."bigquery_table_ttl", s0."api_quota", s0."webhook_notification_url", s0."slack_hook_url", s0."bq_table_partition_type", s0."custom_event_message_keys", s0."log_events_updated_at", s0."notifications_every", s0."lock_schema", s0."validate_schema", s0."drop_lql_filters", s0."drop_lql_string", s0."v2_pipeline", s0."disable_tailing", s0."suggested_keys", s0."transform_copy_fields", s0."user_id", s0."notifications", s0."inserted_at", s0."updated_at" FROM "sources" AS s0 WHERE (s0."id" = $1) SELECT 
  schemaname,
  tablename,
  rowsecurity
FROM pg_tables 
WHERE tablename IN ($1, $2) INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88) -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:48:08.009Z

set local search_path = '' -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:43:58.984Z

set local search_path = '' -- 创建查询用户完整额度信息的函数（从look表查询）
CREATE OR REPLACE FUNCTION get_user_quota_info(
  user_uuid UUID,
  access_password TEXT
) RETURNS JSON AS $$
DECLARE
  user_info RECORD;
  uuid_string TEXT;
  daily_available INTEGER;
  reward_available INTEGER;
BEGIN
  -- 密码校验
  IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
    RAISE EXCEPTION 'Invalid access password';
  END IF;
  
  -- 参数验证
  IF user_uuid IS NULL THEN
    RAISE EXCEPTION 'user_uuid cannot be null';
  END IF;
  
  -- UUID格式验证
  uuid_string := user_uuid::text;
  IF LENGTH(uuid_string) != 36 OR 
     uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid UUID format';
  END IF;
  
  -- 获取用户额度信息（从look表查询）
  SELECT 
    daily_free_quota,
    daily_free_used,
    reward_quota,
    reward_used,
    last_free_reset_date,
    is_verified
  INTO user_info
  FROM "membership-look"
  WHERE user_id = user_uuid;
  
  -- 如果用户不存在
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'message', 'User not found'
    );
  END IF;
  
  -- 检查是否需要重置（但不实际重置，只计算）
  IF user_info.last_free_reset_date < CURRENT_DATE THEN
    daily_available := user_info.daily_free_quota;
    reward_available := user_info.reward_quota;
  ELSE
    daily_available := GREATEST(0, user_info.daily_free_quota - user_info.daily_free_used);
    reward_available := GREATEST(0, user_info.reward_quota - user_info.reward_used);
  END IF;
  
  -- 返回完整信息
  RETURN json_build_object(
    'success', true,
    'is_verified', user_info.is_verified,
    'daily_free_quota', user_info.daily_free_quota,
    'daily_free_used', CASE WHEN user_info.last_free_reset_date < CURRENT_DATE THEN 0 ELSE user_info.daily_free_used END,
    'daily_free_remaining', daily_available,
    'reward_quota', user_info.reward_quota,
    'reward_used', CASE WHEN user_info.last_free_reset_date < CURRENT_DATE THEN 0 ELSE user_info.reward_used END,
    'reward_remaining', reward_available,
    'last_reset_date', user_info.last_free_reset_date,
    'needs_reset', user_info.last_free_reset_date < CURRENT_DATE
  );
END;
$$ LANGUAGE plpgsql -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:48:49.979Z

set local search_path = '' INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:21:49.180Z

set local search_path = '' -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:40:34.447Z

set local search_path = '' SELECT r0."id", r0."sink", r0."token", r0."lql_filters", r0."lql_string", r0."source_id", r0."backend_id", r0."inserted_at", r0."updated_at" FROM "rules" AS r0 WHERE (r0."source_id" = $1) INSERT INTO "_analytics"."log_events_db2f88a6_2f21_4309_8baf_6ff0b23b88dc" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12) SELECT current_user, session_user -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:45:14.943Z

set local search_path = '' with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r SELECT EXISTS (SELECT schema_migrations.* FROM schema_migrations AS schema_migrations WHERE version = $1) -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:46:18.102Z

set local search_path = '' with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44) INSERT INTO "_analytics"."log_events_db2f88a6_2f21_4309_8baf_6ff0b23b88dc" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32) SELECT 
    NOW() AT TIME ZONE $1 as current_time,
    COUNT(*) as total_users,
    SUM(word_count_used) as total_word_count_used
FROM "membership-true" CREATE TABLE IF NOT EXISTS log_events_55791b5c_70ae_4b06_b866_3f500b18b61a (
  id TEXT PRIMARY KEY,
  body JSONB,
  event_message TEXT,
  timestamp TIMESTAMP
) -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:38:39.649Z

set local search_path = '' -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:25:09.548Z

set local search_path = '' COMMIT SELECT * FROM cron.job_run_details
    WHERE jobid = $1
    ORDER BY start_time DESC
    LIMIT $2 WITH pgrst_source AS (SELECT pgrst_call.pgrst_scalar FROM (SELECT $1 AS json_data) pgrst_payload, LATERAL (SELECT "user_uuid_param", "prompt_id_param", "access_password" FROM json_to_record(pgrst_payload.json_data) AS _("user_uuid_param" uuid, "prompt_id_param" uuid, "access_password" text) LIMIT $2) pgrst_body , LATERAL (SELECT "public"."add_author_daily_quota_for_prompt_usage"("user_uuid_param" := pgrst_body."user_uuid_param", "prompt_id_param" := pgrst_body."prompt_id_param", "access_password" := pgrst_body."access_password") pgrst_scalar) pgrst_call) SELECT $3::bigint AS total_result_set, $4 AS page_total, coalesce(json_agg(_postgrest_t.pgrst_scalar)->$5, $6) AS body, nullif(current_setting($7, $8), $9) AS response_headers, nullif(current_setting($10, $11), $12) AS response_status, $13 AS response_inserted FROM (SELECT "add_author_daily_quota_for_prompt_usage".* FROM "pgrst_source" AS "add_author_daily_quota_for_prompt_usage"     ) _postgrest_t -- 添加注释说明字段用途
COMMENT ON COLUMN "membership-look".reward_quota IS '奖励额度总量' -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:13:11.368Z

set local search_path = '' CREATE INDEX IF NOT EXISTS log_events_db2f88a6_2f21_4309_8baf_6ff0b23b88dc_timestamp_brin_idx ON log_events_db2f88a6_2f21_4309_8baf_6ff0b23b88dc USING brin (timestamp) DELETE FROM sessions WHERE user_id = $1 INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16) INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112),($113,$114,$115,$116),($117,$118,$119,$120),($121,$122,$123,$124),($125,$126,$127,$128),($129,$130,$131,$132),($133,$134,$135,$136),($137,$138,$139,$140),($141,$142,$143,$144),($145,$146,$147,$148),($149,$150,$151,$152),($153,$154,$155,$156),($157,$158,$159,$160),($161,$162,$163,$164),($165,$166,$167,$168),($169,$170,$171,$172),($173,$174,$175,$176),($177,$178,$179,$180),($181,$182,$183,$184),($185,$186,$187,$188),($189,$190,$191,$192),($193,$194,$195,$196),($197,$198,$199,$200),($201,$202,$203,$204),($205,$206,$207,$208),($209,$210,$211,$212),($213,$214,$215,$216),($217,$218,$219,$220),($221,$222,$223,$224),($225,$226,$227,$228),($229,$230,$231,$232),($233,$234,$235,$236),($237,$238,$239,$240),($241,$242,$243,$244),($245,$246,$247,$248),($249,$250,$251,$252),($253,$254,$255,$256),($257,$258,$259,$260),($261,$262,$263,$264),($265,$266,$267,$268),($269,$270,$271,$272),($273,$274,$275,$276),($277,$278,$279,$280),($281,$282,$283,$284),($285,$286,$287,$288),($289,$290,$291,$292),($293,$294,$295,$296),($297,$298,$299,$300),($301,$302,$303,$304),($305,$306,$307,$308),($309,$310,$311,$312),($313,$314,$315,$316),($317,$318,$319,$320),($321,$322,$323,$324),($325,$326,$327,$328) COMMIT select setting from pg_settings where name = $1 SELECT user_id, email FROM "membership-true" WHERE email = $1 with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r CREATE TABLE IF NOT EXISTS supabase_migrations.schema_migrations (
                  version TEXT PRIMARY KEY,
                  name TEXT,
                  applied_at TIMESTAMPTZ DEFAULT NOW()
                ) with _base_query as (select * from public."membership-look" order by "membership-look".id asc nulls last limit $1 offset $2)
  select id,case
        when octet_length(email::text) > $3 
        then left(email::text, $4) || $5
        else email::text
      end as email,case
        when octet_length(membership_level::text) > $6 
        then left(membership_level::text, $7) || $8
        else membership_level::text
      end as membership_level,word_count_limit,word_count_used,created_at,updated_at,user_id,is_verified,daily_free_quota,daily_free_used,last_free_reset_date,reward_quota,reward_used from _base_query -- Recursively get the base types of domains
  WITH
  base_types AS (
    WITH RECURSIVE
    recurse AS (
      SELECT
        oid,
        typbasetype,
        COALESCE(NULLIF(typbasetype, $3), oid) AS base
      FROM pg_type
      UNION
      SELECT
        t.oid,
        b.typbasetype,
        COALESCE(NULLIF(b.typbasetype, $4), b.oid) AS base
      FROM recurse t
      JOIN pg_type b ON t.typbasetype = b.oid
    )
    SELECT
      oid,
      base
    FROM recurse
    WHERE typbasetype = $5
  ),
  arguments AS (
    SELECT
      oid,
      array_agg((
        COALESCE(name, $6), -- name
        type::regtype::text, -- type
        CASE type
          WHEN $7::regtype THEN $8
          WHEN $9::regtype THEN $10
          WHEN $11::regtype THEN $12
          WHEN $13::regtype THEN $14
          ELSE type::regtype::text
        END, -- convert types that ignore the lenth and accept any value till maximum size
        idx <= (pronargs - pronargdefaults), -- is_required
        COALESCE(mode = $15, $16) -- is_variadic
      ) ORDER BY idx) AS args,
      CASE COUNT(*) - COUNT(name) -- number of unnamed arguments
        WHEN $17 THEN $18
        WHEN $19 THEN (array_agg(type))[$20] IN ($21::regtype, $22::regtype, $23::regtype, $24::regtype, $25::regtype)
        ELSE $26
      END AS callable
    FROM pg_proc,
         unnest(proargnames, proargtypes, proargmodes)
           WITH ORDINALITY AS _ (name, type, mode, idx)
    WHERE type IS NOT NULL -- only input arguments
    GROUP BY oid
  )
  SELECT
    pn.nspname AS proc_schema,
    p.proname AS proc_name,
    d.description AS proc_description,
    COALESCE(a.args, $27) AS args,
    tn.nspname AS schema,
    COALESCE(comp.relname, t.typname) AS name,
    p.proretset AS rettype_is_setof,
    (t.typtype = $28
     -- if any TABLE, INOUT or OUT arguments present, treat as composite
     or COALESCE(proargmodes::text[] && $29, $30)
    ) AS rettype_is_composite,
    bt.oid <> bt.base as rettype_is_composite_alias,
    p.provolatile,
    p.provariadic > $31 as hasvariadic,
    lower((regexp_split_to_array((regexp_split_to_array(iso_config, $32))[$33], $34))[$35]) AS transaction_isolation_level,
    coalesce(func_settings.kvs, $36) as kvs
  FROM pg_proc p
  LEFT JOIN arguments a ON a.oid = p.oid
  JOIN pg_namespace pn ON pn.oid = p.pronamespace
  JOIN base_types bt ON bt.oid = p.prorettype
  JOIN pg_type t ON t.oid = bt.base
  JOIN pg_namespace tn ON tn.oid = t.typnamespace
  LEFT JOIN pg_class comp ON comp.oid = t.typrelid
  LEFT JOIN pg_description as d ON d.objoid = p.oid
  LEFT JOIN LATERAL unnest(proconfig) iso_config ON iso_config LIKE $37
  LEFT JOIN LATERAL (
    SELECT
      array_agg(row(
        substr(setting, $38, strpos(setting, $39) - $40),
        substr(setting, strpos(setting, $41) + $42)
      )) as kvs
    FROM unnest(proconfig) setting
    WHERE setting ~ ANY($2)
  ) func_settings ON $43
  WHERE t.oid <> $44::regtype AND COALESCE(a.callable, $45)
AND prokind = $46 AND pn.nspname = ANY($1) DROP FUNCTION IF EXISTS get_user_quota_info(UUID, TEXT) SELECT id, title, created_by FROM "propmt-zhanshi" WHERE id = $1 LISTEN "pgrst" INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112),($113,$114,$115,$116),($117,$118,$119,$120),($121,$122,$123,$124),($125,$126,$127,$128),($129,$130,$131,$132),($133,$134,$135,$136),($137,$138,$139,$140),($141,$142,$143,$144),($145,$146,$147,$148),($149,$150,$151,$152),($153,$154,$155,$156),($157,$158,$159,$160),($161,$162,$163,$164) WITH pgrst_source AS ( SELECT "public"."membership-look".* FROM "public"."membership-look"  WHERE  "public"."membership-look"."user_id" = $1    )  SELECT $2::bigint AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, coalesce(json_agg(_postgrest_t)->$3, $4) AS body, nullif(current_setting($5, $6), $7) AS response_headers, nullif(current_setting($8, $9), $10) AS response_status, $11 AS response_inserted FROM ( SELECT * FROM pgrst_source ) _postgrest_t COMMIT with records as (
      select
        c.oid::int8 as "id",
        nc.nspname as "schema",
        c.relname as "name",
        c.relkind as "type",
        case c.relkind
          when $1 then $2
          when $3 then $4
          when $5 then $6
          when $7 then $8
          when $9 then $10
        end as "type_sort",
        obj_description(c.oid) as "comment",
        count(*) over() as "count",
        c.relrowsecurity as "rls_enabled"
      from
        pg_namespace nc
        join pg_class c on nc.oid = c.relnamespace
      where
        c.relkind in ($11, $12, $13, $14, $15)
        and not pg_is_other_temp_schema(nc.oid)
        and (
          pg_has_role(c.relowner, $16)
          or has_table_privilege(
            c.oid,
            $17
          )
          or has_any_column_privilege(c.oid, $18)
        )
        and nc.nspname in ($19)
        
      order by c.relname asc
      limit $20
      offset $21
    )
    select
      jsonb_build_object(
        $22, coalesce(jsonb_agg(
          jsonb_build_object(
            $23, r.id,
            $24, r.schema,
            $25, r.name,
            $26, r.type,
            $27, r.comment,
            $28, r.rls_enabled
          )
          order by r.name asc
        ), $29::jsonb),
        $30, coalesce(min(r.count), $31)
      ) "data"
    from records r SELECT jobid, jobname, schedule, command, active 
FROM cron.job INSERT INTO "extensions" ("type","settings","tenant_external_id","inserted_at","updated_at","id") VALUES ($1,$2,$3,$4,$5,$6) CREATE INDEX IF NOT EXISTS log_events_55791b5c_70ae_4b06_b866_3f500b18b61a_timestamp_brin_idx ON log_events_55791b5c_70ae_4b06_b866_3f500b18b61a USING brin (timestamp) update cron.job_run_details set status = $1 where runid = $2 -- 查看重置结果
SELECT 
  email,
  daily_free_used, 
  reward_used,
  last_free_reset_date::text as reset_date
FROM "membership-true" 
WHERE email = $1 -- 添加注释说明字段用途
COMMENT ON COLUMN "membership-true".reward_quota IS '奖励额度总量' WITH    rows AS (      SELECT ctid      FROM net._http_response      WHERE created < now() - $1      ORDER BY created      LIMIT $2    )    DELETE FROM net._http_response r    USING rows WHERE r.ctid = rows.ctid -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:30:02.904Z

set local search_path = '' with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r select * from schema_migrations SELECT 
  email,
  reward_quota,
  reward_used,
  reward_quota - reward_used as remaining_reward
FROM "membership-true" 
WHERE email = $1 SELECT current_setting($1)::integer, current_setting($2), version() COMMENT ON COLUMN "membership-look".reward_used IS '奖励额度已使用量（从true表同步）' with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r SELECT
      c.castsource::regtype::text,
      c.casttarget::regtype::text,
      c.castfunc::regproc::text
    FROM
      pg_catalog.pg_cast c
    JOIN pg_catalog.pg_type src_t
      ON c.castsource::oid = src_t.oid
    JOIN pg_catalog.pg_type dst_t
      ON c.casttarget::oid = dst_t.oid
    WHERE
      c.castcontext = $1
      AND c.castmethod = $2
      AND has_function_privilege(c.castfunc, $3)
      AND ((src_t.typtype = $4 AND c.casttarget IN ($5::regtype::oid , $6::regtype::oid))
       OR (dst_t.typtype = $7 AND c.castsource IN ($8::regtype::oid , $9::regtype::oid))) -- 删除测试重置函数
DROP FUNCTION IF EXISTS public.test_reset_all_quota() -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:44:02.150Z

set local search_path = '' INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36) COMMIT with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r SELECT table_name, column_name FROM information_schema.columns WHERE table_name IN ($1, $2) AND column_name = $3 -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:25:50.556Z

set local search_path = '' SELECT reset_membership_word_count_test() SET client_min_messages TO WARNING SELECT 
    jobid, 
    runid, 
    job_pid, 
    database, 
    username, 
    command, 
    status, 
    return_message, 
    start_time, 
    end_time
FROM cron.job_run_details 
WHERE jobid = $1
ORDER BY start_time DESC 
LIMIT $2 SELECT id, email, word_count_used FROM "membership-true" LIMIT $1 -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:46:58.406Z

set local search_path = '' SELECT t0."id", t0."db_host", t0."db_port", t0."db_database", t0."external_id", t0."default_parameter_status", t0."ip_version", t0."upstream_ssl", t0."upstream_verify", t0."upstream_tls_ca", t0."enforce_ssl", t0."require_user", t0."auth_query", t0."default_pool_size", t0."sni_hostname", t0."default_max_clients", t0."client_idle_timeout", t0."client_heartbeat_interval", t0."allow_list", t0."availability_zone", t0."inserted_at", t0."updated_at" FROM "_supavisor"."tenants" AS t0 WHERE (t0."external_id" = $1) DROP TYPE IF EXISTS pg_temp.tabledefs CASCADE -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:47:00.562Z

set local search_path = '' SELECT e0."id", e0."token", e0."name", e0."query", e0."description", e0."language", e0."source_mapping", e0."sandboxable", e0."cache_duration_seconds", e0."proactive_requerying_seconds", e0."max_limit", e0."enable_auth", e0."user_id", e0."inserted_at", e0."updated_at" FROM "endpoint_queries" AS e0 WHERE (e0."user_id" = $1) select (select count(*) from public."propmt-neirong"), $1 as is_estimate SELECT 
  column_name,
  data_type,
  column_default
FROM information_schema.columns 
WHERE table_name = $1 
AND column_name LIKE $2 OR column_name LIKE $3 -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:13:02.441Z

set local search_path = '' with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r DELETE FROM "tenants" WHERE "id" = $1 INSERT INTO "audit_log_entries" ("created_at", "id", "instance_id", "ip_address", "payload") VALUES ($1, $2, $3, $4, $5) with tables as (SELECT
  c.oid :: int8 AS id,
  nc.nspname AS schema,
  c.relname AS name,
  c.relrowsecurity AS rls_enabled,
  c.relforcerowsecurity AS rls_forced,
  CASE
    WHEN c.relreplident = $1 THEN $2
    WHEN c.relreplident = $3 THEN $4
    WHEN c.relreplident = $5 THEN $6
    ELSE $7
  END AS replica_identity,
  pg_total_relation_size(format($8, nc.nspname, c.relname)) :: int8 AS bytes,
  pg_size_pretty(
    pg_total_relation_size(format($9, nc.nspname, c.relname))
  ) AS size,
  pg_stat_get_live_tuples(c.oid) AS live_rows_estimate,
  pg_stat_get_dead_tuples(c.oid) AS dead_rows_estimate,
  obj_description(c.oid) AS comment,
  coalesce(pk.primary_keys, $10) as primary_keys,
  coalesce(
    jsonb_agg(relationships) filter (where relationships is not null),
    $11
  ) as relationships
FROM
  pg_namespace nc
  JOIN pg_class c ON nc.oid = c.relnamespace
  left join (
    select
      table_id,
      jsonb_agg(_pk.*) as primary_keys
    from (
      select
        n.nspname as schema,
        c.relname as table_name,
        a.attname as name,
        c.oid :: int8 as table_id
      from
        pg_index i,
        pg_class c,
        pg_attribute a,
        pg_namespace n
      where
        i.indrelid = c.oid
        and c.relnamespace = n.oid
        and a.attrelid = c.oid
        and a.attnum = any (i.indkey)
        and i.indisprimary
    ) as _pk
    group by table_id
  ) as pk
  on pk.table_id = c.oid
  left join (
    select
      c.oid :: int8 as id,
      c.conname as constraint_name,
      nsa.nspname as source_schema,
      csa.relname as source_table_name,
      sa.attname as source_column_name,
      nta.nspname as target_table_schema,
      cta.relname as target_table_name,
      ta.attname as target_column_name
    from
      pg_constraint c
    join (
      pg_attribute sa
      join pg_class csa on sa.attrelid = csa.oid
      join pg_namespace nsa on csa.relnamespace = nsa.oid
    ) on sa.attrelid = c.conrelid and sa.attnum = any (c.conkey)
    join (
      pg_attribute ta
      join pg_class cta on ta.attrelid = cta.oid
      join pg_namespace nta on cta.relnamespace = nta.oid
    ) on ta.attrelid = c.confrelid and ta.attnum = any (c.confkey)
    where
      c.contype = $12
  ) as relationships
  on (relationships.source_schema = nc.nspname and relationships.source_table_name = c.relname)
  or (relationships.target_table_schema = nc.nspname and relationships.target_table_name = c.relname)
WHERE
  c.relkind IN ($13, $14)
  AND NOT pg_is_other_temp_schema(nc.oid)
  AND (
    pg_has_role(c.relowner, $15)
    OR has_table_privilege(
      c.oid,
      $16
    )
    OR has_any_column_privilege(c.oid, $17)
  )
group by
  c.oid,
  c.relname,
  c.relrowsecurity,
  c.relforcerowsecurity,
  c.relreplident,
  nc.nspname,
  pk.primary_keys
)
  
select
  *
  
from tables where schema IN ($18) INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64) -- 手动测试新的重置函数
SELECT test_reset_all_quota() as reset_count -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:21:08.741Z

set local search_path = '' with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112),($113,$114,$115,$116),($117,$118,$119,$120),($121,$122,$123,$124),($125,$126,$127,$128),($129,$130,$131,$132),($133,$134,$135,$136),($137,$138,$139,$140),($141,$142,$143,$144),($145,$146,$147,$148),($149,$150,$151,$152),($153,$154,$155,$156),($157,$158,$159,$160),($161,$162,$163,$164),($165,$166,$167,$168),($169,$170,$171,$172),($173,$174,$175,$176),($177,$178,$179,$180),($181,$182,$183,$184),($185,$186,$187,$188),($189,$190,$191,$192),($193,$194,$195,$196),($197,$198,$199,$200) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r SELECT name FROM pg_timezone_names -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:48:27.897Z

set local search_path = '' with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r SELECT 
  email,
  daily_free_used, 
  daily_free_quota,
  reward_used,
  reward_quota,
  last_free_reset_date::text as reset_date,
  is_verified
FROM "membership-true" 
WHERE is_verified = $1
ORDER BY email CREATE TABLE IF NOT EXISTS "_supavisor"."schema_migrations" ("version" bigint, "inserted_at" timestamp(0), PRIMARY KEY ("version")) WITH pgrst_source AS ( SELECT "public"."propmt-zhanshi"."id", "public"."propmt-zhanshi"."title", "public"."propmt-zhanshi"."description", "public"."propmt-zhanshi"."category", "public"."propmt-zhanshi"."type", "public"."propmt-zhanshi"."created_at", "public"."propmt-zhanshi"."updated_at", "public"."propmt-zhanshi"."created_by", "public"."propmt-zhanshi"."author_display_id", "public"."propmt-zhanshi"."usage_count" FROM "public"."propmt-zhanshi"  WHERE  "public"."propmt-zhanshi"."category" = $1 AND  "public"."propmt-zhanshi"."type" = $2  ORDER BY "public"."propmt-zhanshi"."updated_at" DESC  LIMIT $3 OFFSET $4 )  SELECT $5::bigint AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, coalesce(json_agg(_postgrest_t), $6) AS body, nullif(current_setting($7, $8), $9) AS response_headers, nullif(current_setting($10, $11), $12) AS response_status, $13 AS response_inserted FROM ( SELECT * FROM pgrst_source ) _postgrest_t SELECT count(*) FROM "endpoint_queries" AS e0 WHERE (e0."user_id" = $1) select "id", "name", "public", "owner", "created_at", "updated_at", "file_size_limit", "allowed_mime_types" from "buckets" update public."membership-true" set (daily_free_quota) = (select daily_free_quota from json_populate_record($1::public."membership-true", $2)) where id = $3 SELECT p0."id", p0."name", p0."stripe_id", p0."price", p0."period", p0."limit_sources", p0."limit_rate_limit", p0."limit_source_rate_limit", p0."limit_alert_freq", p0."limit_saved_search_limit", p0."limit_team_users_limit", p0."limit_source_fields_limit", p0."limit_source_ttl", p0."type", p0."inserted_at", p0."updated_at" FROM "plans" AS p0 WHERE (p0."name" = $1) update public."membership-look" set (daily_free_quota) = (select daily_free_quota from json_populate_record($1::public."membership-look", $2)) where id = $3 COMMIT with _base_query as (select * from public."membership-look" order by "membership-look".id asc nulls last limit $1 offset $2)
  select id,case
        when octet_length(email::text) > $3 
        then left(email::text, $4) || $5
        else email::text
      end as email,case
        when octet_length(membership_level::text) > $6 
        then left(membership_level::text, $7) || $8
        else membership_level::text
      end as membership_level,word_count_limit,word_count_used,created_at,updated_at,user_id,is_verified,daily_free_quota,daily_free_used,last_free_reset_date from _base_query SELECT proname, prosrc FROM pg_proc WHERE proname LIKE $1 OR proname LIKE $2 -- 恢复一些测试数据，以便观察定时任务效果
UPDATE "membership-true" 
SET 
  daily_free_used = $1,
  reward_used = $2
WHERE is_verified = $3 update public."membership-look" set (is_verified) = (select is_verified from json_populate_record($1::public."membership-look", $2)) where id = $3 SELECT * FROM migrations WHERE id <= $1 SELECT
    tbl.schemaname,
    tbl.tablename,
    tbl.quoted_name,
    tbl.is_table,
    json_agg(a) as columns
  FROM
    (
      SELECT
        n.nspname as schemaname,
        c.relname as tablename,
        (quote_ident(n.nspname) || $1 || quote_ident(c.relname)) as quoted_name,
        $2 as is_table
      FROM
        pg_catalog.pg_class c
        JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
      WHERE
        c.relkind = $3
        AND n.nspname not in ($4, $5, $6)
        AND n.nspname not like $7
        AND n.nspname not like $8
        AND has_schema_privilege(n.oid, $9) = $10
        AND has_table_privilege(quote_ident(n.nspname) || $11 || quote_ident(c.relname), $12) = $13
      union all
      SELECT
        n.nspname as schemaname,
        c.relname as tablename,
        (quote_ident(n.nspname) || $14 || quote_ident(c.relname)) as quoted_name,
        $15 as is_table
      FROM
        pg_catalog.pg_class c
        JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
      WHERE
        c.relkind in ($16, $17)
        AND n.nspname not in ($18, $19, $20)
        AND n.nspname not like $21
        AND n.nspname not like $22
        AND has_schema_privilege(n.oid, $23) = $24
        AND has_table_privilege(quote_ident(n.nspname) || $25 || quote_ident(c.relname), $26) = $27
    ) as tbl
    LEFT JOIN (
      SELECT
        attrelid,
        attname,
        format_type(atttypid, atttypmod) as data_type,
        attnum,
        attisdropped
      FROM
        pg_attribute
    ) as a ON (
      a.attrelid = tbl.quoted_name::regclass
      AND a.attnum > $28
      AND NOT a.attisdropped
      AND has_column_privilege(tbl.quoted_name, a.attname, $29)
    )
  
  GROUP BY schemaname, tablename, quoted_name, is_table CREATE TABLE IF NOT EXISTS log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4 (
  id TEXT PRIMARY KEY,
  body JSONB,
  event_message TEXT,
  timestamp TIMESTAMP
) update public."membership-true" set (word_count_used) = (select word_count_used from json_populate_record($1::public."membership-true", $2)) where id = $3 -- source: dashboard
-- user: self host
-- date: 2025-08-02T09:52:04.709Z

set local search_path = '' select pid, query, query_start from pg_stat_activity where state = $1 and datname = $2 SELECT u0."id", u0."email", u0."provider", u0."token", u0."api_key", u0."old_api_key", u0."email_preferred", u0."name", u0."image", u0."email_me_product", u0."admin", u0."phone", u0."bigquery_project_id", u0."bigquery_dataset_location", u0."bigquery_dataset_id", u0."bigquery_udfs_hash", u0."bigquery_processed_bytes_limit", u0."api_quota", u0."valid_google_account", u0."provider_uid", u0."company", u0."billing_enabled", u0."endpoints_beta", u0."metadata", u0."preferences", u0."partner_upgraded", u0."partner_id", u0."inserted_at", u0."updated_at" FROM "users" AS u0 WHERE ((u0."provider_uid" = $1) AND (u0."provider" = $2)) -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:24:56.881Z

set local search_path = '' -- 创建测试重置函数，立刻重置所有用户的两个字段
-- 不检查日期条件，强制重置所有认证用户

CREATE OR REPLACE FUNCTION public.test_reset_all_quota()
 RETURNS integer
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  reset_count INTEGER := 0;
BEGIN
  -- 立刻重置所有认证用户的每日免费额度和奖励额度（无日期条件）
  UPDATE "membership-true"
  SET 
    daily_free_used = 0,
    reward_used = 0,
    last_free_reset_date = CURRENT_DATE,
    updated_at = NOW()
  WHERE 
    is_verified = TRUE;  -- 移除日期条件，强制重置所有用户
  
  GET DIAGNOSTICS reset_count = ROW_COUNT;
  RETURN reset_count;
END;
$function$ DROP FUNCTION IF EXISTS add_reward_quota(UUID, INTEGER, TEXT) with _base_query as (select * from public.novel_files order by novel_files.id asc nulls last limit $1 offset $2)
  select id,user_id,case
        when octet_length(file_name::text) > $3 
        then left(file_name::text, $4) || $5
        else file_name::text
      end as file_name,case
        when octet_length(file_path::text) > $6 
        then left(file_path::text, $7) || $8
        else file_path::text
      end as file_path,file_size,case
        when octet_length(mime_type::text) > $9 
        then left(mime_type::text, $10) || $11
        else mime_type::text
      end as mime_type,upload_time,case
        when octet_length(minio_bucket::text) > $12 
        then left(minio_bucket::text, $13) || $14
        else minio_bucket::text
      end as minio_bucket,case
        when octet_length(minio_object_key::text) > $15 
        then left(minio_object_key::text, $16) || $17
        else minio_object_key::text
      end as minio_object_key,created_at,case
        when octet_length(work_title::text) > $18 
        then left(work_title::text, $19) || $20
        else work_title::text
      end as work_title from _base_query with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r select * from cron.job order by jobid SELECT s0."id", s0."name", s0."service_name", s0."token", s0."public_token", s0."favorite", s0."bigquery_table_ttl", s0."api_quota", s0."webhook_notification_url", s0."slack_hook_url", s0."bq_table_partition_type", s0."custom_event_message_keys", s0."log_events_updated_at", s0."notifications_every", s0."lock_schema", s0."validate_schema", s0."drop_lql_filters", s0."drop_lql_string", s0."v2_pipeline", s0."disable_tailing", s0."suggested_keys", s0."transform_copy_fields", s0."user_id", s0."notifications", s0."inserted_at", s0."updated_at" FROM "sources" AS s0 WHERE ((s0."name" = $1) AND (s0."user_id" = $2)) -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:25:08.785Z

set local search_path = '' SELECT s0."version" FROM "schema_migrations" AS s0 -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:21:09.626Z

set local search_path = '' INSERT INTO "_analytics"."log_events_7915a5ef_6f63_4d10_8209_3453b035cc75" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112),($113,$114,$115,$116),($117,$118,$119,$120),($121,$122,$123,$124),($125,$126,$127,$128),($129,$130,$131,$132),($133,$134,$135,$136),($137,$138,$139,$140),($141,$142,$143,$144),($145,$146,$147,$148),($149,$150,$151,$152),($153,$154,$155,$156),($157,$158,$159,$160),($161,$162,$163,$164),($165,$166,$167,$168),($169,$170,$171,$172),($173,$174,$175,$176),($177,$178,$179,$180),($181,$182,$183,$184),($185,$186,$187,$188),($189,$190,$191,$192),($193,$194,$195,$196),($197,$198,$199,$200),($201,$202,$203,$204),($205,$206,$207,$208),($209,$210,$211,$212),($213,$214,$215,$216),($217,$218,$219,$220),($221,$222,$223,$224),($225,$226,$227,$228),($229,$230,$231,$232),($233,$234,$235,$236),($237,$238,$239,$240),($241,$242,$243,$244),($245,$246,$247,$248),($249,$250,$251,$252),($253,$254,$255,$256),($257,$258,$259,$260),($261,$262,$263,$264),($265,$266,$267,$268),($269,$270,$271,$272),($273,$274,$275,$276),($277,$278,$279,$280),($281,$282,$283,$284),($285,$286,$287,$288),($289,$290,$291,$292),($293,$294,$295,$296),($297,$298,$299,$300),($301,$302,$303,$304),($305,$306,$307,$308),($309,$310,$311,$312),($313,$314,$315,$316),($317,$318,$319,$320),($321,$322,$323,$324),($325,$326,$327,$328),($329,$330,$331,$332),($333,$334,$335,$336),($337,$338,$339,$340),($341,$342,$343,$344),($345,$346,$347,$348),($349,$350,$351,$352),($353,$354,$355,$356),($357,$358,$359,$360),($361,$362,$363,$364),($365,$366,$367,$368),($369,$370,$371,$372),($373,$374,$375,$376),($377,$378,$379,$380),($381,$382,$383,$384),($385,$386,$387,$388),($389,$390,$391,$392),($393,$394,$395,$396),($397,$398,$399,$400),($401,$402,$403,$404),($405,$406,$407,$408),($409,$410,$411,$412),($413,$414,$415,$416),($417,$418,$419,$420),($421,$422,$423,$424),($425,$426,$427,$428),($429,$430,$431,$432),($433,$434,$435,$436),($437,$438,$439,$440),($441,$442,$443,$444),($445,$446,$447,$448),($449,$450,$451,$452),($453,$454,$455,$456),($457,$458,$459,$460),($461,$462,$463,$464),($465,$466,$467,$468) -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:48:44.828Z

set local search_path = '' LOCK TABLE "_supavisor"."schema_migrations" IN SHARE UPDATE EXCLUSIVE MODE LISTEN "REQUEST_LOCK_RELEASE" -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:48:05.202Z

set local search_path = '' INSERT INTO "_analytics"."log_events_d880e866_2184_4823_8a8c_f3af54ad1bfd" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8) INSERT INTO "_analytics"."log_events_db2f88a6_2f21_4309_8baf_6ff0b23b88dc" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48) DROP FUNCTION IF EXISTS pg_temp.pg_get_tabledef(character varying,character varying,boolean,tabledefs[]) CREATE INDEX IF NOT EXISTS log_events_7915a5ef_6f63_4d10_8209_3453b035cc75_timestamp_brin_idx ON log_events_7915a5ef_6f63_4d10_8209_3453b035cc75 USING brin (timestamp) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r WITH pgrst_source AS (SELECT pgrst_call.pgrst_scalar FROM (SELECT $1 AS json_data) pgrst_payload, LATERAL (SELECT "user_uuid", "deduct_amount", "access_password" FROM json_to_record(pgrst_payload.json_data) AS _("user_uuid" uuid, "deduct_amount" integer, "access_password" text) LIMIT $2) pgrst_body , LATERAL (SELECT "public"."deduct_daily_free_quota"("user_uuid" := pgrst_body."user_uuid", "deduct_amount" := pgrst_body."deduct_amount", "access_password" := pgrst_body."access_password") pgrst_scalar) pgrst_call) SELECT $3::bigint AS total_result_set, $4 AS page_total, coalesce(json_agg(_postgrest_t.pgrst_scalar)->$5, $6) AS body, nullif(current_setting($7, $8), $9) AS response_headers, nullif(current_setting($10, $11), $12) AS response_status, $13 AS response_inserted FROM (SELECT "deduct_daily_free_quota".* FROM "pgrst_source" AS "deduct_daily_free_quota"     ) _postgrest_t with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r CREATE TABLE IF NOT EXISTS log_events_db2f88a6_2f21_4309_8baf_6ff0b23b88dc (
  id TEXT PRIMARY KEY,
  body JSONB,
  event_message TEXT,
  timestamp TIMESTAMP
) INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52) SELECT t.oid, t.typname, t.typsend, t.typreceive, t.typoutput, t.typinput,
       coalesce(d.typelem, t.typelem), coalesce(r.rngsubtype, $1), ARRAY (
  SELECT a.atttypid
  FROM pg_attribute AS a
  WHERE a.attrelid = t.typrelid AND a.attnum > $2 AND NOT a.attisdropped
  ORDER BY a.attnum
)

FROM pg_type AS t
LEFT JOIN pg_type AS d ON t.typbasetype = d.oid
LEFT JOIN pg_range AS r ON r.rngtypid = t.oid OR r.rngmultitypid = t.oid OR (t.typbasetype <> $3 AND r.rngtypid = t.typbasetype)
WHERE (t.typrelid = $4)
AND (t.typelem = $5 OR NOT EXISTS (SELECT $6 FROM pg_catalog.pg_type s WHERE s.typrelid != $7 AND s.oid = t.typelem)) SELECT 
  $1 as table_name, 
  COUNT(*) as count,
  COUNT(CASE WHEN user_id = $2 THEN $3 END) as target_user_count
FROM "membership-true"
UNION ALL
SELECT 
  $4 as table_name, 
  COUNT(*) as count,
  COUNT(CASE WHEN user_id = $5 THEN $6 END) as target_user_count
FROM "membership-look" UPDATE "refresh_tokens" AS refresh_tokens SET "revoked" = $1, "updated_at" = $2 WHERE refresh_tokens.id = $3 BEGIN with recursive
      pks_fks as (
        -- pk + fk referencing col
        select
          contype::text as contype,
          conname,
          array_length(conkey, $3) as ncol,
          conrelid as resorigtbl,
          col as resorigcol,
          ord
        from pg_constraint
        left join lateral unnest(conkey) with ordinality as _(col, ord) on $4
        where contype IN ($5, $6)
        union
        -- fk referenced col
        select
          concat(contype, $7) as contype,
          conname,
          array_length(confkey, $8) as ncol,
          confrelid,
          col,
          ord
        from pg_constraint
        left join lateral unnest(confkey) with ordinality as _(col, ord) on $9
        where contype=$10
      ),
      views as (
        select
          c.oid       as view_id,
          n.nspname   as view_schema,
          c.relname   as view_name,
          r.ev_action as view_definition
        from pg_class c
        join pg_namespace n on n.oid = c.relnamespace
        join pg_rewrite r on r.ev_class = c.oid
        where c.relkind in ($11, $12) and n.nspname = ANY($1 || $2)
      ),
      transform_json as (
        select
          view_id, view_schema, view_name,
          -- the following formatting is without indentation on purpose
          -- to allow simple diffs, with less whitespace noise
          replace(
            replace(
            replace(
            replace(
            replace(
            replace(
            replace(
            regexp_replace(
            replace(
            replace(
            replace(
            replace(
            replace(
            replace(
            replace(
            replace(
            replace(
            replace(
            replace(
              view_definition::text,
            -- This conversion to json is heavily optimized for performance.
            -- The general idea is to use as few regexp_replace() calls as possible.
            -- Simple replace() is a lot faster, so we jump through some hoops
            -- to be able to use regexp_replace() only once.
            -- This has been tested against a huge schema with 250+ different views.
            -- The unit tests do NOT reflect all possible inputs. Be careful when changing this!
            -- -----------------------------------------------
            -- pattern           | replacement         | flags
            -- -----------------------------------------------
            -- `<>` in pg_node_tree is the same as `null` in JSON, but due to very poor performance of json_typeof
            -- we need to make this an empty array here to prevent json_array_elements from throwing an error
            -- when the targetList is null.
            -- We'll need to put it first, to make the node protection below work for node lists that start with
            -- null: `(<> ...`, too. This is the case for coldefexprs, when the first column does not have a default value.
               $13              , $14
            -- `,` is not part of the pg_node_tree format, but used in the regex.
            -- This removes all `,` that might be part of column names.
            ), $15               , $16
            -- The same applies for `{` and `}`, although those are used a lot in pg_node_tree.
            -- We remove the escaped ones, which might be part of column names again.
            ), $17            , $18
            ), $19            , $20
            -- The fields we need are formatted as json manually to protect them from the regex.
            ), $21   , $22
            ), $23        , $24
            ), $25   , $26
            ), $27   , $28
            -- Make the regex also match the node type, e.g. `{QUERY ...`, to remove it in one pass.
            ), $29               , $30
            -- Protect node lists, which start with `({` or `((` from the greedy regex.
            -- The extra `{` is removed again later.
            ), $31              , $32
            ), $33              , $34
            -- This regex removes all unused fields to avoid the need to format all of them correctly.
            -- This leads to a smaller json result as well.
            -- Removal stops at `,` for used fields (see above) and `}` for the end of the current node.
            -- Nesting can't be parsed correctly with a regex, so we stop at `{` as well and
            -- add an empty key for the followig node.
            ), $35       , $36              , $37
            -- For performance, the regex also added those empty keys when hitting a `,` or `}`.
            -- Those are removed next.
            ), $38           , $39
            ), $40           , $41
            -- This reverses the "node list protection" from above.
            ), $42              , $43
            -- Every key above has been added with a `,` so far. The first key in an object doesn't need it.
            ), $44              , $45
            -- pg_node_tree has `()` around lists, but JSON uses `[]`
            ), $46               , $47
            ), $48               , $49
            -- pg_node_tree has ` ` between list items, but JSON uses `,`
            ), $50             , $51
          )::json as view_definition
        from views
      ),
      target_entries as(
        select
          view_id, view_schema, view_name,
          json_array_elements(view_definition->$52->$53) as entry
        from transform_json
      ),
      results as(
        select
          view_id, view_schema, view_name,
          (entry->>$54)::int as view_column,
          (entry->>$55)::oid as resorigtbl,
          (entry->>$56)::int as resorigcol
        from target_entries
      ),
      -- CYCLE detection according to PG docs: https://www.postgresql.org/docs/current/queries-with.html#QUERIES-WITH-CYCLE
      -- Can be replaced with CYCLE clause once PG v13 is EOL.
      recursion(view_id, view_schema, view_name, view_column, resorigtbl, resorigcol, is_cycle, path) as(
        select
          r.*,
          $57,
          ARRAY[resorigtbl]
        from results r
        where view_schema = ANY ($1)
        union all
        select
          view.view_id,
          view.view_schema,
          view.view_name,
          view.view_column,
          tab.resorigtbl,
          tab.resorigcol,
          tab.resorigtbl = ANY(path),
          path || tab.resorigtbl
        from recursion view
        join results tab on view.resorigtbl=tab.view_id and view.resorigcol=tab.view_column
        where not is_cycle
      ),
      repeated_references as(
        select
          view_id,
          view_schema,
          view_name,
          resorigtbl,
          resorigcol,
          array_agg(attname) as view_columns
        from recursion
        join pg_attribute vcol on vcol.attrelid = view_id and vcol.attnum = view_column
        group by
          view_id,
          view_schema,
          view_name,
          resorigtbl,
          resorigcol
      )
      select
        sch.nspname as table_schema,
        tbl.relname as table_name,
        rep.view_schema,
        rep.view_name,
        pks_fks.conname as constraint_name,
        pks_fks.contype as constraint_type,
        array_agg(row(col.attname, view_columns) order by pks_fks.ord) as column_dependencies
      from repeated_references rep
      join pks_fks using (resorigtbl, resorigcol)
      join pg_class tbl on tbl.oid = rep.resorigtbl
      join pg_attribute col on col.attrelid = tbl.oid and col.attnum = rep.resorigcol
      join pg_namespace sch on sch.oid = tbl.relnamespace
      group by sch.nspname, tbl.relname,  rep.view_schema, rep.view_name, pks_fks.conname, pks_fks.contype, pks_fks.ncol
      -- make sure we only return key for which all columns are referenced in the view - no partial PKs or FKs
      having ncol = array_length(array_agg(row(col.attname, view_columns) order by pks_fks.ord), $58) -- 删除错误添加到look表的字段
ALTER TABLE "membership-look" 
DROP COLUMN IF EXISTS reward_quota,
DROP COLUMN IF EXISTS reward_used update public."membership-true" set (is_verified) = (select is_verified from json_populate_record($1::public."membership-true", $2)) where id = $3 SELECT mfa_amr_claims.authentication_method, mfa_amr_claims.created_at, mfa_amr_claims.id, mfa_amr_claims.session_id, mfa_amr_claims.updated_at FROM mfa_amr_claims AS mfa_amr_claims WHERE session_id = $1 LOCK TABLE "schema_migrations" IN SHARE UPDATE EXCLUSIVE MODE -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:19:35.553Z

set local search_path = '' with f as (
      
-- CTE with sane arg_modes, arg_names, and arg_types.
-- All three are always of the same length.
-- All three include all args, including OUT and TABLE args.
with functions as (
  select
    *,
    -- proargmodes is null when all arg modes are IN
    coalesce(
      p.proargmodes,
      array_fill($1::text, array[cardinality(coalesce(p.proallargtypes, p.proargtypes))])
    ) as arg_modes,
    -- proargnames is null when all args are unnamed
    coalesce(
      p.proargnames,
      array_fill($2::text, array[cardinality(coalesce(p.proallargtypes, p.proargtypes))])
    ) as arg_names,
    -- proallargtypes is null when all arg modes are IN
    coalesce(p.proallargtypes, p.proargtypes) as arg_types,
    array_cat(
      array_fill($3, array[pronargs - pronargdefaults]),
      array_fill($4, array[pronargdefaults])) as arg_has_defaults
  from
    pg_proc as p
  where
    p.prokind = $5
)
select
  f.oid as id,
  n.nspname as schema,
  f.proname as name,
  l.lanname as language,
  case
    when l.lanname = $6 then $7
    else f.prosrc
  end as definition,
  case
    when l.lanname = $8 then f.prosrc
    else pg_get_functiondef(f.oid)
  end as complete_statement,
  coalesce(f_args.args, $9) as args,
  pg_get_function_arguments(f.oid) as argument_types,
  pg_get_function_identity_arguments(f.oid) as identity_argument_types,
  f.prorettype as return_type_id,
  pg_get_function_result(f.oid) as return_type,
  nullif(rt.typrelid, $10) as return_type_relation_id,
  f.proretset as is_set_returning_function,
  case
    when f.provolatile = $11 then $12
    when f.provolatile = $13 then $14
    when f.provolatile = $15 then $16
  end as behavior,
  f.prosecdef as security_definer,
  f_config.config_params as config_params
from
  functions f
  left join pg_namespace n on f.pronamespace = n.oid
  left join pg_language l on f.prolang = l.oid
  left join pg_type rt on rt.oid = f.prorettype
  left join (
    select
      oid,
      jsonb_object_agg(param, value) filter (where param is not null) as config_params
    from
      (
        select
          oid,
          (string_to_array(unnest(proconfig), $17))[$18] as param,
          (string_to_array(unnest(proconfig), $19))[$20] as value
        from
          functions
      ) as t
    group by
      oid
  ) f_config on f_config.oid = f.oid
  left join (
    select
      oid,
      jsonb_agg(jsonb_build_object(
        $21, t2.mode,
        $22, name,
        $23, type_id,
        -- Cast null into false boolean
        $24, COALESCE(has_default, $25)
      )) as args
    from
      (
        select
          oid,
          unnest(arg_modes) as mode,
          unnest(arg_names) as name,
          -- Coming from: coalesce(p.proallargtypes, p.proargtypes) postgres won't automatically assume
          -- integer, we need to cast it to be properly parsed
          unnest(arg_types)::int8 as type_id,
          unnest(arg_has_defaults) as has_default
        from
          functions
      ) as t1,
      lateral (
        select
          case
            when t1.mode = $26 then $27
            when t1.mode = $28 then $29
            when t1.mode = $30 then $31
            when t1.mode = $32 then $33
            else $34
          end as mode
      ) as t2
    group by
      t1.oid
  ) f_args on f_args.oid = f.oid

    )
    select
      f.*
    from f
   where schema NOT IN ($35,$36,$37) -- 查看true表和look表的数据结构
SELECT $1 as table_name, user_id, reward_quota, reward_used 
FROM "membership-true" 
WHERE user_id = $2
UNION ALL
SELECT $3 as table_name, user_id, reward_quota, reward_used 
FROM "membership-look" 
WHERE user_id = $4 SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = $1 
ORDER BY ordinal_position with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r SELECT reset_daily_free_quota() with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- 查看现有的deduct_daily_free_quota函数定义
SELECT routine_name, routine_definition 
FROM information_schema.routines 
WHERE routine_name = $1 
AND routine_type = $2 CREATE TABLE IF NOT EXISTS log_events_4c7f3dd0_52d7_4004_9490_4a8fe5cdd3f4 (
  id TEXT PRIMARY KEY,
  body JSONB,
  event_message TEXT,
  timestamp TIMESTAMP
) -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:20:06.486Z

set local search_path = '' -- 在membership-look表添加奖励额度字段
ALTER TABLE "membership-look" 
ADD COLUMN reward_quota INTEGER DEFAULT 0,
ADD COLUMN reward_used INTEGER DEFAULT 0 INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48) CREATE INDEX IF NOT EXISTS log_events_253abd96_ed02_48c8_bed8_1bae068350c0_timestamp_brin_idx ON log_events_253abd96_ed02_48c8_bed8_1bae068350c0 USING brin (timestamp) INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28) select count(*) from auth.users -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:25:07.432Z

set local search_path = '' -- 检查触发器同步结果
SELECT $1 as table_name, daily_free_used, reward_used
FROM "membership-true" 
WHERE user_id = $2
UNION ALL
SELECT $3 as table_name, daily_free_used, reward_used
FROM "membership-look" 
WHERE user_id = $4 -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:10:48.645Z

set local search_path = '' with table_info as (
    select 
      n.nspname::text as schema,
      c.relname::text as name,
      to_regclass(concat($1, n.nspname, $2, c.relname, $3)) as regclass
    from pg_class c
    join pg_namespace n on n.oid = c.relnamespace
    where c.oid = $4
)
select 
    con.oid as id,
    con.conname as name,
    con.contype as type
from pg_catalog.pg_constraint con
inner join pg_catalog.pg_class rel
        on rel.oid = con.conrelid
inner join pg_catalog.pg_namespace nsp
        on nsp.oid = connamespace
inner join table_info ti
        on ti.schema = nsp.nspname 
        and ti.name = rel.relname select (select count(*) from public.novel_files), $1 as is_estimate -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:13:14.983Z

set local search_path = '' -- 修复作者奖励函数，处理提示词表中created_by字段存储邮箱而非UUID的问题

CREATE OR REPLACE FUNCTION public.add_author_daily_quota_for_prompt_usage(user_uuid_param uuid, prompt_id_param uuid, access_password text)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  author_email TEXT;
  author_user_id UUID;
  uuid_string TEXT;
  prompt_uuid_string TEXT;
BEGIN
  -- 密码校验
  IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
    RAISE EXCEPTION 'Invalid access password';
  END IF;
  
  -- 参数验证
  IF user_uuid_param IS NULL THEN
    RAISE EXCEPTION 'user_uuid_param cannot be null';
  END IF;
  
  IF prompt_id_param IS NULL THEN
    RAISE EXCEPTION 'prompt_id_param cannot be null';
  END IF;
  
  -- UUID格式验证
  uuid_string := user_uuid_param::text;
  IF LENGTH(uuid_string) != 36 OR 
     uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid user UUID format';
  END IF;
  
  prompt_uuid_string := prompt_id_param::text;
  IF LENGTH(prompt_uuid_string) != 36 OR 
     prompt_uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid prompt UUID format';
  END IF;
  
  -- 获取提示词作者的邮箱地址（因为created_by字段存储的是邮箱）
  SELECT created_by INTO author_email
  FROM "propmt-zhanshi"
  WHERE id = prompt_id_param;
  
  -- 如果提示词不存在
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- 通过邮箱地址查找作者的UUID
  SELECT user_id INTO author_user_id
  FROM "membership-true"
  WHERE email = author_email AND is_verified = TRUE;
  
  -- 如果作者不存在或未认证
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- 如果是作者自己使用自己的提示词，不给奖励
  IF author_user_id = user_uuid_param THEN
    RETURN FALSE;
  END IF;
  
  -- 为作者添加1次奖励额度（操作true表，触发器会自动同步到look表）
  UPDATE "membership-true"
  SET 
    reward_quota = COALESCE(reward_quota, 0) + 1,
    updated_at = NOW()
  WHERE user_id = author_user_id AND is_verified = TRUE;
  
  -- 检查是否更新成功
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$function$ SELECT pg_try_advisory_lock($1) -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:19:45.165Z

set local search_path = '' SELECT s0."id", s0."bigquery_schema", s0."schema_flat_map", s0."source_id", s0."inserted_at", s0."updated_at" FROM "source_schemas" AS s0 WHERE (s0."source_id" = $1) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112),($113,$114,$115,$116),($117,$118,$119,$120),($121,$122,$123,$124),($125,$126,$127,$128),($129,$130,$131,$132),($133,$134,$135,$136),($137,$138,$139,$140),($141,$142,$143,$144) -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:13:12.570Z

set local search_path = '' INSERT INTO "_analytics"."log_events_d880e866_2184_4823_8a8c_f3af54ad1bfd" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24) select
  t.oid::int8 as id,
  t.typname as name,
  n.nspname as schema,
  format_type (t.oid, $1) as format,
  coalesce(t_enums.enums, $2) as enums,
  coalesce(t_attributes.attributes, $3) as attributes,
  obj_description (t.oid, $4) as comment
from
  pg_type t
  left join pg_namespace n on n.oid = t.typnamespace
  left join (
    select
      enumtypid,
      jsonb_agg(enumlabel order by enumsortorder) as enums
    from
      pg_enum
    group by
      enumtypid
  ) as t_enums on t_enums.enumtypid = t.oid
  left join (
    select
      oid,
      jsonb_agg(
        jsonb_build_object($5, a.attname, $6, a.atttypid::int8)
        order by a.attnum asc
      ) as attributes
    from
      pg_class c
      join pg_attribute a on a.attrelid = c.oid
    where
      c.relkind = $7 and not a.attisdropped
    group by
      c.oid
  ) as t_attributes on t_attributes.oid = t.typrelid

    where
      (
        t.typrelid = $8
        or (
          select
            c.relkind = $9
          from
            pg_class c
          where
            c.oid = t.typrelid
        )
      )
     and not exists (
                 select
                 from
                   pg_type el
                 where
                   el.oid = t.typelem
                   and el.typarray = t.oid
               ) and n.nspname NOT IN ($10,$11,$12) WITH pgrst_source AS ( SELECT "public"."membership-look"."word_count_used", "public"."membership-look"."word_count_limit", "public"."membership-look"."membership_level", "public"."membership-look"."is_verified", "public"."membership-look"."daily_free_quota", "public"."membership-look"."daily_free_used", "public"."membership-look"."reward_quota", "public"."membership-look"."reward_used", "public"."membership-look"."last_free_reset_date" FROM "public"."membership-look"  WHERE  "public"."membership-look"."user_id" = $1    )  SELECT $2::bigint AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, coalesce(json_agg(_postgrest_t)->$3, $4) AS body, nullif(current_setting($5, $6), $7) AS response_headers, nullif(current_setting($8, $9), $10) AS response_status, $11 AS response_inserted FROM ( SELECT * FROM pgrst_source ) _postgrest_t -- 确认测试函数已删除
SELECT proname FROM pg_proc WHERE proname = $1 -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:46:53.243Z

set local search_path = '' -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:45:58.443Z

set local search_path = '' with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r SET search_path=_analytics INSERT INTO "billing_counts" ("count","node","source_id","user_id","inserted_at","updated_at") VALUES ($1,$2,$3,$4,$5,$6) RETURNING "id" CREATE INDEX IF NOT EXISTS log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4_timestamp_brin_idx ON log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4 USING brin (timestamp) SELECT * FROM "sessions" WHERE id = $1 LIMIT $2 FOR UPDATE SKIP LOCKED SELECT e0."id", e0."type", e0."settings", e0."tenant_external_id", e0."inserted_at", e0."updated_at", e0."tenant_external_id" FROM "extensions" AS e0 WHERE (e0."tenant_external_id" = $1) ORDER BY e0."tenant_external_id" -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:03:53.236Z

set local search_path = '' with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- 检查PostgreSQL日志相关设置
SHOW log_statement SELECT NOW() as current_time, CURRENT_DATE as current_date, $1::date as stored_date DROP FUNCTION IF EXISTS add_author_daily_quota_for_prompt_usage(UUID, UUID, TEXT) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r set search_path to "storage","public","extensions" UPDATE "users" AS users SET "last_sign_in_at" = $1, "updated_at" = $2 WHERE users.id = $3 -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:20:09.450Z

set local search_path = '' select set_config('search_path', $1, true), set_config($2, $3, true), set_config('role', $4, true), set_config('request.jwt.claims', $5, true), set_config('request.method', $6, true), set_config('request.path', $7, true), set_config('request.headers', $8, true), set_config('request.cookies', $9, true), set_config($10, $11, true), set_config($12, $13, true) INSERT INTO "_analytics"."log_events_d880e866_2184_4823_8a8c_f3af54ad1bfd" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12) INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112),($113,$114,$115,$116),($117,$118,$119,$120),($121,$122,$123,$124),($125,$126,$127,$128),($129,$130,$131,$132),($133,$134,$135,$136),($137,$138,$139,$140),($141,$142,$143,$144),($145,$146,$147,$148),($149,$150,$151,$152),($153,$154,$155,$156),($157,$158,$159,$160) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- 查看用户额度状态
SELECT 
  email,
  daily_free_used, 
  reward_used
FROM "membership-true" 
WHERE is_verified = $1
ORDER BY email -- 创建重置word_count_used字段的测试函数
CREATE OR REPLACE FUNCTION reset_membership_word_count_test()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    affected_rows INTEGER;
    execution_time TIMESTAMP WITH TIME ZONE;
BEGIN
    -- 记录执行时间
    execution_time := NOW();
    
    -- 更新所有membership-true表中用户的word_count_used字段为0
    UPDATE "membership-true" 
    SET 
        word_count_used = 0,
        updated_at = execution_time;
    
    -- 获取受影响的行数
    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    
    -- 返回执行结果
    RETURN format('测试函数执行成功！执行时间: %s (UTC), 重置了 %s 个用户的word_count_used字段', 
                  execution_time, affected_rows);
END;
$$ with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- 在membership-true表添加奖励额度字段
ALTER TABLE "membership-true" 
ADD COLUMN reward_quota INTEGER DEFAULT 0,
ADD COLUMN reward_used INTEGER DEFAULT 0 commit INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32) SHOW shared_preload_libraries with _base_query as (select * from public."membership-true" order by "membership-true".id asc nulls last limit $1 offset $2)
  select id,case
        when octet_length(email::text) > $3 
        then left(email::text, $4) || $5
        else email::text
      end as email,case
        when octet_length(membership_level::text) > $6 
        then left(membership_level::text, $7) || $8
        else membership_level::text
      end as membership_level,word_count_limit,word_count_used,created_at,updated_at,user_id,is_verified,daily_free_quota,daily_free_used,last_free_reset_date,reward_quota,reward_used from _base_query with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112),($113,$114,$115,$116),($117,$118,$119,$120),($121,$122,$123,$124),($125,$126,$127,$128),($129,$130,$131,$132),($133,$134,$135,$136),($137,$138,$139,$140),($141,$142,$143,$144),($145,$146,$147,$148),($149,$150,$151,$152),($153,$154,$155,$156),($157,$158,$159,$160),($161,$162,$163,$164),($165,$166,$167,$168),($169,$170,$171,$172),($173,$174,$175,$176),($177,$178,$179,$180),($181,$182,$183,$184),($185,$186,$187,$188),($189,$190,$191,$192),($193,$194,$195,$196),($197,$198,$199,$200),($201,$202,$203,$204),($205,$206,$207,$208),($209,$210,$211,$212),($213,$214,$215,$216),($217,$218,$219,$220),($221,$222,$223,$224),($225,$226,$227,$228),($229,$230,$231,$232),($233,$234,$235,$236),($237,$238,$239,$240),($241,$242,$243,$244),($245,$246,$247,$248),($249,$250,$251,$252),($253,$254,$255,$256),($257,$258,$259,$260),($261,$262,$263,$264),($265,$266,$267,$268),($269,$270,$271,$272),($273,$274,$275,$276),($277,$278,$279,$280),($281,$282,$283,$284),($285,$286,$287,$288),($289,$290,$291,$292),($293,$294,$295,$296),($297,$298,$299,$300),($301,$302,$303,$304),($305,$306,$307,$308),($309,$310,$311,$312),($313,$314,$315,$316),($317,$318,$319,$320),($321,$322,$323,$324),($325,$326,$327,$328),($329,$330,$331,$332),($333,$334,$335,$336),($337,$338,$339,$340),($341,$342,$343,$344),($345,$346,$347,$348),($349,$350,$351,$352),($353,$354,$355,$356),($357,$358,$359,$360),($361,$362,$363,$364),($365,$366,$367,$368),($369,$370,$371,$372),($373,$374,$375,$376),($377,$378,$379,$380),($381,$382,$383,$384),($385,$386,$387,$388),($389,$390,$391,$392),($393,$394,$395,$396),($397,$398,$399,$400),($401,$402,$403,$404),($405,$406,$407,$408),($409,$410,$411,$412),($413,$414,$415,$416),($417,$418,$419,$420),($421,$422,$423,$424),($425,$426,$427,$428),($429,$430,$431,$432),($433,$434,$435,$436),($437,$438,$439,$440),($441,$442,$443,$444),($445,$446,$447,$448),($449,$450,$451,$452),($453,$454,$455,$456),($457,$458,$459,$460),($461,$462,$463,$464),($465,$466,$467,$468),($469,$470,$471,$472),($473,$474,$475,$476),($477,$478,$479,$480),($481,$482,$483,$484) update cron.job_run_details set job_pid = $1, status = $2 where runid = $3 -- 检查cron数据库设置
SHOW cron.database_name LOCK TABLE "realtime"."schema_migrations" IN SHARE UPDATE EXCLUSIVE MODE LISTEN "supavisor_local_2_5" INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112),($113,$114,$115,$116),($117,$118,$119,$120),($121,$122,$123,$124),($125,$126,$127,$128),($129,$130,$131,$132),($133,$134,$135,$136),($137,$138,$139,$140),($141,$142,$143,$144),($145,$146,$147,$148),($149,$150,$151,$152),($153,$154,$155,$156),($157,$158,$159,$160),($161,$162,$163,$164),($165,$166,$167,$168),($169,$170,$171,$172),($173,$174,$175,$176),($177,$178,$179,$180),($181,$182,$183,$184),($185,$186,$187,$188),($189,$190,$191,$192) -- 先消耗完每日免费额度，然后测试奖励额度扣除
UPDATE "membership-true" 
SET daily_free_used = $1, updated_at = NOW()
WHERE user_id = $2 SELECT r0."id", r0."sink", r0."token", r0."lql_filters", r0."lql_string", r0."source_id", r0."backend_id", r0."inserted_at", r0."updated_at", r0."source_id" FROM "rules" AS r0 WHERE (r0."source_id" = $1) ORDER BY r0."source_id" INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112),($113,$114,$115,$116),($117,$118,$119,$120),($121,$122,$123,$124),($125,$126,$127,$128),($129,$130,$131,$132),($133,$134,$135,$136),($137,$138,$139,$140),($141,$142,$143,$144),($145,$146,$147,$148),($149,$150,$151,$152),($153,$154,$155,$156),($157,$158,$159,$160),($161,$162,$163,$164),($165,$166,$167,$168),($169,$170,$171,$172),($173,$174,$175,$176) INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104) -- 查看重置后的用户状态
SELECT 
  email,
  daily_free_used, 
  reward_used,
  last_free_reset_date::text as reset_date
FROM "membership-true" 
WHERE is_verified = $1
ORDER BY email select (select count(*) from public."membership-look"), $1 as is_estimate -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:13:13.824Z

set local search_path = '' INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60) -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:13:20.290Z

set local search_path = '' do $$
begin
  execute
    replace(
      replace(
        replace(
          replace(
            pg_read_file(
'/etc/postgresql-custom/extension-custom-scripts/pg_cron/after-create.sql'            ),
            '@extname@', '''pg_cron'''          ),
          '@extschema@', '''pg_catalog'''        ),
        '@extversion@', 'null'      ),     '@extcascade@', 'false'    );
exception
  when undefined_file then
    -- skip
end
$$ -- 2. 修改 deduct_user_word_count 函数
CREATE OR REPLACE FUNCTION public.deduct_user_word_count(user_uuid uuid, deduct_amount integer, access_password text)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER  -- 以定义者权限运行
AS $function$
DECLARE
    current_used INTEGER;
    current_limit INTEGER;
    uuid_string TEXT;
BEGIN
    -- 密码校验
    IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
        RAISE EXCEPTION 'Invalid access password';
    END IF;
    
    -- 参数验证
    IF user_uuid IS NULL THEN
        RAISE EXCEPTION 'user_uuid cannot be null';
    END IF;
    
    -- 严格的UUID格式验证
    uuid_string := user_uuid::text;
    IF LENGTH(uuid_string) != 36 OR 
       uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
        RAISE EXCEPTION 'Invalid UUID format';
    END IF;
    
    IF deduct_amount IS NULL OR deduct_amount <= 0 THEN
        RAISE EXCEPTION 'deduct_amount must be positive';
    END IF;
    
    -- 防止恶意大量扣除
    IF deduct_amount > 1000000 THEN
        RAISE EXCEPTION 'deduct_amount too large (max: 1000000)';
    END IF;
    
    -- 获取当前用户的字数使用情况
    SELECT word_count_used, word_count_limit
    INTO current_used, current_limit
    FROM "membership-true"
    WHERE user_id = user_uuid AND is_verified = TRUE;
    
    -- 如果用户不存在或未认证
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- 检查余额是否足够
    IF current_used + deduct_amount > current_limit THEN
        RETURN FALSE;
    END IF;
    
    -- 扣除字数
    UPDATE "membership-true"
    SET 
        word_count_used = current_used + deduct_amount,
        updated_at = NOW()
    WHERE user_id = user_uuid;
    
    RETURN TRUE;
END;
$function$ CREATE SCHEMA IF NOT EXISTS _analytics -- 确认数据重置成功
SELECT $1 as table_name, daily_free_quota, daily_free_used, reward_quota, reward_used
FROM "membership-true" 
WHERE user_id = $2
UNION ALL
SELECT $3 as table_name, daily_free_quota, daily_free_used, reward_quota, reward_used
FROM "membership-look" 
WHERE user_id = $4 SELECT 
  NOW() as current_time,
  EXTRACT($1 FROM NOW()) as current_hour,
  EXTRACT($2 FROM NOW()) as current_minute SELECT t0."id", t0."name", t0."external_id", t0."jwt_secret", t0."jwt_jwks", t0."postgres_cdc_default", t0."max_concurrent_users", t0."max_events_per_second", t0."max_bytes_per_second", t0."max_channels_per_client", t0."max_joins_per_second", t0."suspend", t0."private_only", t0."inserted_at", t0."updated_at" FROM "tenants" AS t0 WHERE (t0."external_id" = $1) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:20:39.781Z

set local search_path = '' with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- 检查pg_cron后台工作进程
SELECT pid, application_name, state FROM pg_stat_activity WHERE application_name LIKE $1 SELECT daily_free_used, daily_free_quota, reward_used, reward_quota FROM "membership-true" WHERE user_id = $1 SELECT 
  email,
  daily_free_used, 
  daily_free_quota,
  reward_used,
  reward_quota,
  last_free_reset_date,
  CURRENT_DATE as today
FROM "membership-true" 
WHERE user_id = $1 -- 创建添加奖励额度的函数（操作true表）
CREATE OR REPLACE FUNCTION add_reward_quota(
  user_uuid UUID,
  reward_amount INTEGER,
  access_password TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  uuid_string TEXT;
BEGIN
  -- 密码校验
  IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
    RAISE EXCEPTION 'Invalid access password';
  END IF;
  
  -- 参数验证
  IF user_uuid IS NULL THEN
    RAISE EXCEPTION 'user_uuid cannot be null';
  END IF;
  
  -- 严格的UUID格式验证
  uuid_string := user_uuid::text;
  IF LENGTH(uuid_string) != 36 OR 
     uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid UUID format';
  END IF;
  
  IF reward_amount IS NULL OR reward_amount <= 0 THEN
    RAISE EXCEPTION 'reward_amount must be positive';
  END IF;
  
  -- 防止恶意大量添加
  IF reward_amount > 50 THEN
    RAISE EXCEPTION 'reward_amount too large (max: 50)';
  END IF;
  
  -- 添加奖励额度（操作true表，触发器会自动同步到look表）
  UPDATE "membership-true"
  SET 
    reward_quota = COALESCE(reward_quota, 0) + reward_amount,
    updated_at = NOW()
  WHERE user_id = user_uuid AND is_verified = TRUE;
  
  -- 检查是否更新成功
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- 删除测试任务
SELECT cron.unschedule($1) -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:47:44.123Z

set local search_path = '' with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r INSERT INTO "_analytics"."log_events_ea99cbe5_9f1d_40c9_9e0c_11d095808293" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28) SELECT 
  CURRENT_DATE as current_date_raw,
  CURRENT_DATE::text as current_date_text,
  timezone($1, NOW())::date as shanghai_date,
  NOW()::date as now_date SELECT refresh_tokens.created_at, refresh_tokens.id, refresh_tokens.instance_id, refresh_tokens.parent, refresh_tokens.revoked, refresh_tokens.session_id, refresh_tokens.token, refresh_tokens.updated_at, refresh_tokens.user_id FROM refresh_tokens AS refresh_tokens WHERE token = $1 LIMIT $2 SELECT EXISTS (
  SELECT $2
  FROM   pg_catalog.pg_class c
  WHERE  c.relname = $1
  AND    c.relkind = $3
) -- 给用户添加一些奖励额度用于测试
UPDATE "membership-look" 
SET reward_quota = $1, updated_at = NOW()
WHERE user_id = $2 ABORT -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:58:11.194Z

set local search_path = '' SHOW timezone with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:06:06.500Z

set local search_path = '' do $$
begin
  execute
    replace(
      replace(
        replace(
          replace(
            pg_read_file(
'/etc/postgresql-custom/extension-custom-scripts/before-create.sql'            ),
            '@extname@', '''pg_cron'''          ),
          '@extschema@', 'null'        ),
        '@extversion@', 'null'      ),     '@extcascade@', 'false'    );
exception
  when undefined_file then
    -- skip
end
$$ update public."membership-look" set (word_count_used) = (select word_count_used from json_populate_record($1::public."membership-look", $2)) where id = $3 with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r CREATE TABLE IF NOT EXISTS "realtime"."schema_migrations" ("version" bigint, "inserted_at" timestamp(0), PRIMARY KEY ("version")) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r SELECT table_name FROM information_schema.tables WHERE table_schema = $1 AND table_name LIKE $2 -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:14:45.573Z

set local search_path = '' -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:45:16.756Z

set local search_path = '' INSERT INTO supabase_migrations.schema_migrations (version, name) VALUES ($1, $2) ON CONFLICT (version) DO NOTHING -- source: dashboard
-- user: self host
-- date: 2025-08-02T09:54:44.497Z

set local search_path = '' INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92) -- 测试触发器：更新true表，看look表是否同步
UPDATE "membership-true" 
SET reward_quota = $1, updated_at = NOW()
WHERE user_id = $2 SELECT u0."id", u0."email", u0."provider", u0."token", u0."api_key", u0."old_api_key", u0."email_preferred", u0."name", u0."image", u0."email_me_product", u0."admin", u0."phone", u0."bigquery_project_id", u0."bigquery_dataset_location", u0."bigquery_dataset_id", u0."bigquery_udfs_hash", u0."bigquery_processed_bytes_limit", u0."api_quota", u0."valid_google_account", u0."provider_uid", u0."company", u0."billing_enabled", u0."endpoints_beta", u0."metadata", u0."preferences", u0."partner_upgraded", u0."partner_id", u0."inserted_at", u0."updated_at", u0."id" FROM "users" AS u0 WHERE (u0."id" = $1) SELECT u0."id", u0."email", u0."provider", u0."token", u0."api_key", u0."old_api_key", u0."email_preferred", u0."name", u0."image", u0."email_me_product", u0."admin", u0."phone", u0."bigquery_project_id", u0."bigquery_dataset_location", u0."bigquery_dataset_id", u0."bigquery_udfs_hash", u0."bigquery_processed_bytes_limit", u0."api_quota", u0."valid_google_account", u0."provider_uid", u0."company", u0."billing_enabled", u0."endpoints_beta", u0."metadata", u0."preferences", u0."partner_upgraded", u0."partner_id", u0."inserted_at", u0."updated_at" FROM "users" AS u0 INNER JOIN "sources" AS s1 ON s1."user_id" = u0."id" WHERE (s1."log_events_updated_at" >= $1::timestamp + (-$3::decimal::numeric * interval $4)) ORDER BY s1."log_events_updated_at" DESC LIMIT $2 CREATE TABLE IF NOT EXISTS log_events_7915a5ef_6f63_4d10_8209_3453b035cc75 (
  id TEXT PRIMARY KEY,
  body JSONB,
  event_message TEXT,
  timestamp TIMESTAMP
) -- 添加奖励额度相关字段到membership-look表
ALTER TABLE "membership-look" 
ADD COLUMN reward_quota INTEGER DEFAULT 0,
ADD COLUMN reward_used INTEGER DEFAULT 0 SELECT s0."version" FROM "_supavisor"."schema_migrations" AS s0 INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68) -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:10:16.422Z

set local search_path = '' -- 检查pg_cron调度器状态
SELECT pid, application_name, state, backend_start FROM pg_stat_activity WHERE application_name LIKE $1 set search_path=_analytics with
    all_relations as (
      select reltype
      from pg_class
      where relkind in ($1,$2,$3,$4,$5)
    ),
    computed_rels as (
      select
        (parse_ident(p.pronamespace::regnamespace::text))[$6] as schema,
        p.proname::text                  as name,
        arg_schema.nspname::text         as rel_table_schema,
        arg_name.typname::text           as rel_table_name,
        ret_schema.nspname::text         as rel_ftable_schema,
        ret_name.typname::text           as rel_ftable_name,
        not p.proretset or p.prorows = $7 as single_row
      from pg_proc p
        join pg_type      arg_name   on arg_name.oid = p.proargtypes[$8]
        join pg_namespace arg_schema on arg_schema.oid = arg_name.typnamespace
        join pg_type      ret_name   on ret_name.oid = p.prorettype
        join pg_namespace ret_schema on ret_schema.oid = ret_name.typnamespace
      where
        p.pronargs = $9
        and p.proargtypes[$10] in (select reltype from all_relations)
        and p.prorettype in (select reltype from all_relations)
    )
    select
      *,
      row(rel_table_schema, rel_table_name) = row(rel_ftable_schema, rel_ftable_name) as is_self
    from computed_rels with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r SELECT CURRENT_DATE as today, $1::date as last_reset, CURRENT_DATE > $2::date as needs_reset (
with foreign_keys as (
    select
        cl.relnamespace::regnamespace::text as schema_name,
        cl.relname as table_name,
        cl.oid as table_oid,
        ct.conname as fkey_name,
        ct.conkey as col_attnums
    from
        pg_catalog.pg_constraint ct
        join pg_catalog.pg_class cl -- fkey owning table
            on ct.conrelid = cl.oid
        left join pg_catalog.pg_depend d
            on d.objid = cl.oid
            and d.deptype = $1
    where
        ct.contype = $2 -- foreign key constraints
        and d.objid is null -- exclude tables that are dependencies of extensions
        and cl.relnamespace::regnamespace::text not in (
            $3, $4, $5, $6, $7, $8
        )
),
index_ as (
    select
        pi.indrelid as table_oid,
        indexrelid::regclass as index_,
        string_to_array(indkey::text, $9)::smallint[] as col_attnums
    from
        pg_catalog.pg_index pi
    where
        indisvalid
)
select
    $10 as name,
    $11 as title,
    $12 as level,
    $13 as facing,
    array[$14] as categories,
    $15 as description,
    format(
        $16,
        fk.schema_name,
        fk.table_name,
        fk.fkey_name
    ) as detail,
    $17 as remediation,
    jsonb_build_object(
        $18, fk.schema_name,
        $19, fk.table_name,
        $20, $21,
        $22, fk.fkey_name,
        $23, fk.col_attnums
    ) as metadata,
    format($24, fk.schema_name, fk.table_name, fk.fkey_name) as cache_key
from
    foreign_keys fk
    left join index_ idx
        on fk.table_oid = idx.table_oid
        and fk.col_attnums = idx.col_attnums
    left join pg_catalog.pg_depend dep
        on idx.table_oid = dep.objid
        and dep.deptype = $25
where
    idx.index_ is null
    and fk.schema_name not in (
        $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36, $37, $38, $39, $40, $41, $42, $43, $44, $45, $46, $47, $48, $49, $50, $51
    )
    and dep.objid is null -- exclude tables owned by extensions
order by
    fk.schema_name,
    fk.table_name,
    fk.fkey_name)
union all
(
select
    $52 as name,
    $53 as title,
    $54 as level,
    $55 as facing,
    array[$56] as categories,
    $57 as description,
    format(
        $58,
        c.relname
    ) as detail,
    $59 as remediation,
    jsonb_build_object(
        $60, n.nspname,
        $61, c.relname,
        $62, $63,
        $64, array_remove(array_agg(DISTINCT case when pg_catalog.has_table_privilege($65, c.oid, $66) then $67 when pg_catalog.has_table_privilege($68, c.oid, $69) then $70 end), $71)
    ) as metadata,
    format($72, n.nspname, c.relname) as cache_key
from
    -- Identify the oid for auth.users
    pg_catalog.pg_class auth_users_pg_class
    join pg_catalog.pg_namespace auth_users_pg_namespace
        on auth_users_pg_class.relnamespace = auth_users_pg_namespace.oid
        and auth_users_pg_class.relname = $73
        and auth_users_pg_namespace.nspname = $74
    -- Depends on auth.users
    join pg_catalog.pg_depend d
        on d.refobjid = auth_users_pg_class.oid
    join pg_catalog.pg_rewrite r
        on r.oid = d.objid
    join pg_catalog.pg_class c
        on c.oid = r.ev_class
    join pg_catalog.pg_namespace n
        on n.oid = c.relnamespace
    join pg_catalog.pg_class pg_class_auth_users
        on d.refobjid = pg_class_auth_users.oid
where
    d.deptype = $75
    and (
      pg_catalog.has_table_privilege($76, c.oid, $77)
      or pg_catalog.has_table_privilege($78, c.oid, $79)
    )
    and n.nspname = any(array(select trim(unnest(string_to_array(current_setting($80, $81), $82)))))
    -- Exclude self
    and c.relname <> $83
    -- There are 3 insecure configurations
    and
    (
        -- Materialized views don't support RLS so this is insecure by default
        (c.relkind in ($84)) -- m for materialized view
        or
        -- Standard View, accessible to anon or authenticated that is security_definer
        (
            c.relkind = $85 -- v for view
            -- Exclude security invoker views
            and not (
                lower(coalesce(c.reloptions::text,$86))::text[]
                && array[
                    $87,
                    $88,
                    $89,
                    $90
                ]
            )
        )
        or
        -- Standard View, security invoker, but no RLS enabled on auth.users
        (
            c.relkind in ($91) -- v for view
            -- is security invoker
            and (
                lower(coalesce(c.reloptions::text,$92))::text[]
                && array[
                    $93,
                    $94,
                    $95,
                    $96
                ]
            )
            and not pg_class_auth_users.relrowsecurity
        )
    )
group by
    n.nspname,
    c.relname,
    c.oid)
union all
(
with policies as (
    select
        nsp.nspname as schema_name,
        pb.tablename as table_name,
        pc.relrowsecurity as is_rls_active,
        polname as policy_name,
        polpermissive as is_permissive, -- if not, then restrictive
        (select array_agg(r::regrole) from unnest(polroles) as x(r)) as roles,
        case polcmd
            when $97 then $98
            when $99 then $100
            when $101 then $102
            when $103 then $104
            when $105 then $106
        end as command,
        qual,
        with_check
    from
        pg_catalog.pg_policy pa
        join pg_catalog.pg_class pc
            on pa.polrelid = pc.oid
        join pg_catalog.pg_namespace nsp
            on pc.relnamespace = nsp.oid
        join pg_catalog.pg_policies pb
            on pc.relname = pb.tablename
            and nsp.nspname = pb.schemaname
            and pa.polname = pb.policyname
)
select
    $107 as name,
    $108 as title,
    $109 as level,
    $110 as facing,
    array[$111] as categories,
    $112 as description,
    format(
        $113,
        schema_name,
        table_name,
        policy_name
    ) as detail,
    $114 as remediation,
    jsonb_build_object(
        $115, schema_name,
        $116, table_name,
        $117, $118
    ) as metadata,
    format($119, schema_name, table_name, policy_name) as cache_key
from
    policies
where
    is_rls_active
    -- NOTE: does not include realtime in support of monitoring policies on realtime.messages
    and schema_name not in (
        $120, $121, $122, $123, $124, $125, $126, $127, $128, $129, $130, $131, $132, $133, $134, $135, $136, $137, $138, $139, $140, $141, $142, $143, $144
    )
    and (
        -- Example: auth.uid()
        (
            qual like $145
            and lower(qual) not like $146
        )
        or (
            qual like $147
            and lower(qual) not like $148
        )
        or (
            qual like $149
            and lower(qual) not like $150
        )
        or (
            qual like $151
            and lower(qual) not like $152
        )
        or (
            with_check like $153
            and lower(with_check) not like $154
        )
        or (
            with_check like $155
            and lower(with_check) not like $156
        )
        or (
            with_check like $157
            and lower(with_check) not like $158
        )
        or (
            with_check like $159
            and lower(with_check) not like $160
        )
    ))
union all
(
select
    $161 as name,
    $162 as title,
    $163 as level,
    $164 as facing,
    array[$165] as categories,
    $166 as description,
    format(
        $167,
        pgns.nspname,
        pgc.relname
    ) as detail,
    $168 as remediation,
     jsonb_build_object(
        $169, pgns.nspname,
        $170, pgc.relname,
        $171, $172
    ) as metadata,
    format(
        $173,
        pgns.nspname,
        pgc.relname
    ) as cache_key
from
    pg_catalog.pg_class pgc
    join pg_catalog.pg_namespace pgns
        on pgns.oid = pgc.relnamespace
    left join pg_catalog.pg_index pgi
        on pgi.indrelid = pgc.oid
    left join pg_catalog.pg_depend dep
        on pgc.oid = dep.objid
        and dep.deptype = $174
where
    pgc.relkind = $175 -- regular tables
    and pgns.nspname not in (
        $176, $177, $178, $179, $180, $181, $182, $183, $184, $185, $186, $187, $188, $189, $190, $191, $192, $193, $194, $195, $196, $197, $198, $199, $200, $201
    )
    and dep.objid is null -- exclude tables owned by extensions
group by
    pgc.oid,
    pgns.nspname,
    pgc.relname
having
    max(coalesce(pgi.indisprimary, $202)::int) = $203)
union all
(
select
    $204 as name,
    $205 as title,
    $206 as level,
    $207 as facing,
    array[$208] as categories,
    $209 as description,
    format(
        $210,
        psui.indexrelname,
        psui.schemaname,
        psui.relname
    ) as detail,
    $211 as remediation,
    jsonb_build_object(
        $212, psui.schemaname,
        $213, psui.relname,
        $214, $215
    ) as metadata,
    format(
        $216,
        psui.schemaname,
        psui.relname,
        psui.indexrelname
    ) as cache_key

from
    pg_catalog.pg_stat_user_indexes psui
    join pg_catalog.pg_index pi
        on psui.indexrelid = pi.indexrelid
    left join pg_catalog.pg_depend dep
        on psui.relid = dep.objid
        and dep.deptype = $217
where
    psui.idx_scan = $218
    and not pi.indisunique
    and not pi.indisprimary
    and dep.objid is null -- exclude tables owned by extensions
    and psui.schemaname not in (
        $219, $220, $221, $222, $223, $224, $225, $226, $227, $228, $229, $230, $231, $232, $233, $234, $235, $236, $237, $238, $239, $240, $241, $242, $243, $244
    ))
union all
(
select
    $245 as name,
    $246 as title,
    $247 as level,
    $248 as facing,
    array[$249] as categories,
    $250 as description,
    format(
        $251,
        n.nspname,
        c.relname,
        r.rolname,
        act.cmd,
        array_agg(p.polname order by p.polname)
    ) as detail,
    $252 as remediation,
    jsonb_build_object(
        $253, n.nspname,
        $254, c.relname,
        $255, $256
    ) as metadata,
    format(
        $257,
        n.nspname,
        c.relname,
        r.rolname,
        act.cmd
    ) as cache_key
from
    pg_catalog.pg_policy p
    join pg_catalog.pg_class c
        on p.polrelid = c.oid
    join pg_catalog.pg_namespace n
        on c.relnamespace = n.oid
    join pg_catalog.pg_roles r
        on p.polroles @> array[r.oid]
        or p.polroles = array[$258::oid]
    left join pg_catalog.pg_depend dep
        on c.oid = dep.objid
        and dep.deptype = $259,
    lateral (
        select x.cmd
        from unnest((
            select
                case p.polcmd
                    when $260 then array[$261]
                    when $262 then array[$263]
                    when $264 then array[$265]
                    when $266 then array[$267]
                    when $268 then array[$269, $270, $271, $272]
                    else array[$273]
                end as actions
        )) x(cmd)
    ) act(cmd)
where
    c.relkind = $274 -- regular tables
    and p.polpermissive -- policy is permissive
    and n.nspname not in (
        $275, $276, $277, $278, $279, $280, $281, $282, $283, $284, $285, $286, $287, $288, $289, $290, $291, $292, $293, $294, $295, $296, $297, $298, $299, $300
    )
    and r.rolname not like $301
    and r.rolname not like $302
    and not r.rolbypassrls
    and dep.objid is null -- exclude tables owned by extensions
group by
    n.nspname,
    c.relname,
    r.rolname,
    act.cmd
having
    count($303) > $304)
union all
(
select
    $305 as name,
    $306 as title,
    $307 as level,
    $308 as facing,
    array[$309] as categories,
    $310 as description,
    format(
        $311,
        n.nspname,
        c.relname,
        array_agg(p.polname order by p.polname)
    ) as detail,
    $312 as remediation,
    jsonb_build_object(
        $313, n.nspname,
        $314, c.relname,
        $315, $316
    ) as metadata,
    format(
        $317,
        n.nspname,
        c.relname
    ) as cache_key
from
    pg_catalog.pg_policy p
    join pg_catalog.pg_class c
        on p.polrelid = c.oid
    join pg_catalog.pg_namespace n
        on c.relnamespace = n.oid
    left join pg_catalog.pg_depend dep
        on c.oid = dep.objid
        and dep.deptype = $318
where
    c.relkind = $319 -- regular tables
    and n.nspname not in (
        $320, $321, $322, $323, $324, $325, $326, $327, $328, $329, $330, $331, $332, $333, $334, $335, $336, $337, $338, $339, $340, $341, $342, $343, $344, $345
    )
    -- RLS is disabled
    and not c.relrowsecurity
    and dep.objid is null -- exclude tables owned by extensions
group by
    n.nspname,
    c.relname)
union all
(
select
    $346 as name,
    $347 as title,
    $348 as level,
    $349 as facing,
    array[$350] as categories,
    $351 as description,
    format(
        $352,
        n.nspname,
        c.relname
    ) as detail,
    $353 as remediation,
    jsonb_build_object(
        $354, n.nspname,
        $355, c.relname,
        $356, $357
    ) as metadata,
    format(
        $358,
        n.nspname,
        c.relname
    ) as cache_key
from
    pg_catalog.pg_class c
    left join pg_catalog.pg_policy p
        on p.polrelid = c.oid
    join pg_catalog.pg_namespace n
        on c.relnamespace = n.oid
    left join pg_catalog.pg_depend dep
        on c.oid = dep.objid
        and dep.deptype = $359
where
    c.relkind = $360 -- regular tables
    and n.nspname not in (
        $361, $362, $363, $364, $365, $366, $367, $368, $369, $370, $371, $372, $373, $374, $375, $376, $377, $378, $379, $380, $381, $382, $383, $384, $385, $386
    )
    -- RLS is enabled
    and c.relrowsecurity
    and p.polname is null
    and dep.objid is null -- exclude tables owned by extensions
group by
    n.nspname,
    c.relname)
union all
(
select
    $387 as name,
    $388 as title,
    $389 as level,
    $390 as facing,
    array[$391] as categories,
    $392 as description,
    format(
        $393,
        n.nspname,
        c.relname,
        array_agg(pi.indexname order by pi.indexname)
    ) as detail,
    $394 as remediation,
    jsonb_build_object(
        $395, n.nspname,
        $396, c.relname,
        $397, case
            when c.relkind = $398 then $399
            when c.relkind = $400 then $401
            else $402
        end,
        $403, array_agg(pi.indexname order by pi.indexname)
    ) as metadata,
    format(
        $404,
        n.nspname,
        c.relname,
        array_agg(pi.indexname order by pi.indexname)
    ) as cache_key
from
    pg_catalog.pg_indexes pi
    join pg_catalog.pg_namespace n
        on n.nspname  = pi.schemaname
    join pg_catalog.pg_class c
        on pi.tablename = c.relname
        and n.oid = c.relnamespace
    left join pg_catalog.pg_depend dep
        on c.oid = dep.objid
        and dep.deptype = $405
where
    c.relkind in ($406, $407) -- tables and materialized views
    and n.nspname not in (
        $408, $409, $410, $411, $412, $413, $414, $415, $416, $417, $418, $419, $420, $421, $422, $423, $424, $425, $426, $427, $428, $429, $430, $431, $432, $433
    )
    and dep.objid is null -- exclude tables owned by extensions
group by
    n.nspname,
    c.relkind,
    c.relname,
    replace(pi.indexdef, pi.indexname, $434)
having
    count(*) > $435)
union all
(
select
    $436 as name,
    $437 as title,
    $438 as level,
    $439 as facing,
    array[$440] as categories,
    $441 as description,
    format(
        $442,
        n.nspname,
        c.relname
    ) as detail,
    $443 as remediation,
    jsonb_build_object(
        $444, n.nspname,
        $445, c.relname,
        $446, $447
    ) as metadata,
    format(
        $448,
        n.nspname,
        c.relname
    ) as cache_key
from
    pg_catalog.pg_class c
    join pg_catalog.pg_namespace n
        on n.oid = c.relnamespace
    left join pg_catalog.pg_depend dep
        on c.oid = dep.objid
        and dep.deptype = $449
where
    c.relkind = $450
    and (
        pg_catalog.has_table_privilege($451, c.oid, $452)
        or pg_catalog.has_table_privilege($453, c.oid, $454)
    )
    and substring(pg_catalog.version() from $455) >= $456 -- security invoker was added in pg15
    and n.nspname = any(array(select trim(unnest(string_to_array(current_setting($457, $458), $459)))))
    and n.nspname not in (
        $460, $461, $462, $463, $464, $465, $466, $467, $468, $469, $470, $471, $472, $473, $474, $475, $476, $477, $478, $479, $480, $481, $482, $483, $484, $485
    )
    and dep.objid is null -- exclude views owned by extensions
    and not (
        lower(coalesce(c.reloptions::text,$486))::text[]
        && array[
            $487,
            $488,
            $489,
            $490
        ]
    ))
union all
(
select
    $491 as name,
    $492 as title,
    $493 as level,
    $494 as facing,
    array[$495] as categories,
    $496 as description,
    format(
        $497,
        n.nspname,
        p.proname
    ) as detail,
    $498 as remediation,
    jsonb_build_object(
        $499, n.nspname,
        $500, p.proname,
        $501, $502
    ) as metadata,
    format(
        $503,
        n.nspname,
        p.proname,
        md5(p.prosrc) -- required when function is polymorphic
    ) as cache_key
from
    pg_catalog.pg_proc p
    join pg_catalog.pg_namespace n
        on p.pronamespace = n.oid
    left join pg_catalog.pg_depend dep
        on p.oid = dep.objid
        and dep.deptype = $504
where
    n.nspname not in (
        $505, $506, $507, $508, $509, $510, $511, $512, $513, $514, $515, $516, $517, $518, $519, $520, $521, $522, $523, $524, $525, $526, $527, $528, $529, $530
    )
    and dep.objid is null -- exclude functions owned by extensions
    -- Search path not set to ''
    and not coalesce(p.proconfig, $531) && array[$532])
union all
(
select
    $533 as name,
    $534 as title,
    $535 as level,
    $536 as facing,
    array[$537] as categories,
    $538 as description,
    format(
        $539,
        n.nspname,
        c.relname
    ) as detail,
    $540 as remediation,
    jsonb_build_object(
        $541, n.nspname,
        $542, c.relname,
        $543, $544
    ) as metadata,
    format(
        $545,
        n.nspname,
        c.relname
    ) as cache_key
from
    pg_catalog.pg_class c
    join pg_catalog.pg_namespace n
        on c.relnamespace = n.oid
where
    c.relkind = $546 -- regular tables
    -- RLS is disabled
    and not c.relrowsecurity
    and (
        pg_catalog.has_table_privilege($547, c.oid, $548)
        or pg_catalog.has_table_privilege($549, c.oid, $550)
    )
    and n.nspname = any(array(select trim(unnest(string_to_array(current_setting($551, $552), $553)))))
    and n.nspname not in (
        $554, $555, $556, $557, $558, $559, $560, $561, $562, $563, $564, $565, $566, $567, $568, $569, $570, $571, $572, $573, $574, $575, $576, $577, $578, $579
    ))
union all
(
select
    $580 as name,
    $581 as title,
    $582 as level,
    $583 as facing,
    array[$584] as categories,
    $585 as description,
    format(
        $586,
        pe.extname
    ) as detail,
    $587 as remediation,
    jsonb_build_object(
        $588, pe.extnamespace::regnamespace,
        $589, pe.extname,
        $590, $591
    ) as metadata,
    format(
        $592,
        pe.extname
    ) as cache_key
from
    pg_catalog.pg_extension pe
where
    -- plpgsql is installed by default in public and outside user control
    -- confirmed safe
    pe.extname not in ($593)
    -- Scoping this to public is not optimal. Ideally we would use the postgres
    -- search path. That currently isn't available via SQL. In other lints
    -- we have used has_schema_privilege('anon', 'extensions', 'USAGE') but that
    -- is not appropriate here as it would evaluate true for the extensions schema
    and pe.extnamespace::regnamespace::text = $594)
union all
(
with policies as (
    select
        nsp.nspname as schema_name,
        pb.tablename as table_name,
        polname as policy_name,
        qual,
        with_check
    from
        pg_catalog.pg_policy pa
        join pg_catalog.pg_class pc
            on pa.polrelid = pc.oid
        join pg_catalog.pg_namespace nsp
            on pc.relnamespace = nsp.oid
        join pg_catalog.pg_policies pb
            on pc.relname = pb.tablename
            and nsp.nspname = pb.schemaname
            and pa.polname = pb.policyname
)
select
    $595 as name,
    $596 as title,
    $597 as level,
    $598 as facing,
    array[$599] as categories,
    $600 as description,
    format(
        $601,
        schema_name,
        table_name,
        policy_name
    ) as detail,
    $602 as remediation,
    jsonb_build_object(
        $603, schema_name,
        $604, table_name,
        $605, $606
    ) as metadata,
    format($607, schema_name, table_name, policy_name) as cache_key
from
    policies
where
    schema_name not in (
        $608, $609, $610, $611, $612, $613, $614, $615, $616, $617, $618, $619, $620, $621, $622, $623, $624, $625, $626, $627, $628, $629, $630, $631, $632, $633
    )
    and (
        -- Example: auth.jwt() -> 'user_metadata'
        -- False positives are possible, but it isn't practical to string match
        -- If false positive rate is too high, this expression can iterate
        qual like $634
        or qual like $635
        or with_check like $636
        or with_check like $637
    ))
union all
(
select
    $638 as name,
    $639 as title,
    $640 as level,
    $641 as facing,
    array[$642] as categories,
    $643 as description,
    format(
        $644,
        n.nspname,
        c.relname
    ) as detail,
    $645 as remediation,
    jsonb_build_object(
        $646, n.nspname,
        $647, c.relname,
        $648, $649
    ) as metadata,
    format(
        $650,
        n.nspname,
        c.relname
    ) as cache_key
from
    pg_catalog.pg_class c
    join pg_catalog.pg_namespace n
        on n.oid = c.relnamespace
    left join pg_catalog.pg_depend dep
        on c.oid = dep.objid
        and dep.deptype = $651
where
    c.relkind = $652
    and (
        pg_catalog.has_table_privilege($653, c.oid, $654)
        or pg_catalog.has_table_privilege($655, c.oid, $656)
    )
    and n.nspname = any(array(select trim(unnest(string_to_array(current_setting($657, $658), $659)))))
    and n.nspname not in (
        $660, $661, $662, $663, $664, $665, $666, $667, $668, $669, $670, $671, $672, $673, $674, $675, $676, $677, $678, $679, $680, $681, $682, $683, $684, $685
    )
    and dep.objid is null)
union all
(
select
    $686 as name,
    $687 as title,
    $688 as level,
    $689 as facing,
    array[$690] as categories,
    $691 as description,
    format(
        $692,
        n.nspname,
        c.relname
    ) as detail,
    $693 as remediation,
    jsonb_build_object(
        $694, n.nspname,
        $695, c.relname,
        $696, $697
    ) as metadata,
    format(
        $698,
        n.nspname,
        c.relname
    ) as cache_key
from
    pg_catalog.pg_class c
    join pg_catalog.pg_namespace n
        on n.oid = c.relnamespace
    left join pg_catalog.pg_depend dep
        on c.oid = dep.objid
        and dep.deptype = $699
where
    c.relkind = $700
    and (
        pg_catalog.has_table_privilege($701, c.oid, $702)
        or pg_catalog.has_table_privilege($703, c.oid, $704)
    )
    and n.nspname = any(array(select trim(unnest(string_to_array(current_setting($705, $706), $707)))))
    and n.nspname not in (
        $708, $709, $710, $711, $712, $713, $714, $715, $716, $717, $718, $719, $720, $721, $722, $723, $724, $725, $726, $727, $728, $729, $730, $731, $732, $733
    )
    and dep.objid is null)
union all
(
select
    $734 as name,
    $735 as title,
    $736 as level,
    $737 as facing,
    array[$738] as categories,
    $739 as description,
    format(
        $740,
        n.nspname,
        c.relname,
        a.attname,
        t.typname
    ) as detail,
    $741 as remediation,
    jsonb_build_object(
        $742, n.nspname,
        $743, c.relname,
        $744, a.attname,
        $745, $746
    ) as metadata,
    format(
        $747,
        n.nspname,
        c.relname,
        a.attname
    ) AS cache_key
from
    pg_catalog.pg_attribute a
    join pg_catalog.pg_class c
        on a.attrelid = c.oid
    join pg_catalog.pg_namespace n
        on c.relnamespace = n.oid
    join pg_catalog.pg_type t
        on a.atttypid = t.oid
    join pg_catalog.pg_namespace tn
        on t.typnamespace = tn.oid
where
    tn.nspname = $748
    and t.typname in ($749, $750, $751, $752, $753, $754, $755, $756)
    and n.nspname not in ($757, $758, $759)) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r SELECT * FROM cron.job_run_details ORDER BY start_time DESC LIMIT $1 SELECT id, email, word_count_used, updated_at FROM "membership-true" ORDER BY email INSERT INTO "_analytics"."log_events_7915a5ef_6f63_4d10_8209_3453b035cc75" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112),($113,$114,$115,$116),($117,$118,$119,$120),($121,$122,$123,$124),($125,$126,$127,$128),($129,$130,$131,$132),($133,$134,$135,$136),($137,$138,$139,$140),($141,$142,$143,$144),($145,$146,$147,$148),($149,$150,$151,$152),($153,$154,$155,$156),($157,$158,$159,$160),($161,$162,$163,$164),($165,$166,$167,$168),($169,$170,$171,$172),($173,$174,$175,$176),($177,$178,$179,$180),($181,$182,$183,$184) -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:25:09.164Z

set local search_path = '' with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:48:13.771Z

set local search_path = '' -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:49:07.415Z

set local search_path = '' with
      all_relations as (
        select reltype
        from pg_class
        where relkind in ($2,$3,$4,$5,$6)
        union
        select oid
        from pg_type
        where typname = $7
      ),
      media_types as (
          SELECT
            t.oid,
            lower(t.typname) as typname,
            b.oid as base_oid,
            b.typname AS basetypname,
            t.typnamespace,
            case t.typname
              when $8 then $9
              else t.typname
            end as resolved_media_type
          FROM pg_type t
          JOIN pg_type b ON t.typbasetype = b.oid
          WHERE
            t.typbasetype <> $10 and
            (t.typname ~* $11 or t.typname = $12)
      )
      select
        proc_schema.nspname           as handler_schema,
        proc.proname                  as handler_name,
        arg_schema.nspname::text      as target_schema,
        arg_name.typname::text        as target_name,
        media_types.typname           as media_type,
        media_types.resolved_media_type
      from media_types
        join pg_proc      proc         on proc.prorettype = media_types.oid
        join pg_namespace proc_schema  on proc_schema.oid = proc.pronamespace
        join pg_aggregate agg          on agg.aggfnoid = proc.oid
        join pg_type      arg_name     on arg_name.oid = proc.proargtypes[$13]
        join pg_namespace arg_schema   on arg_schema.oid = arg_name.typnamespace
      where
        proc_schema.nspname = ANY($1) and
        proc.pronargs = $14 and
        arg_name.oid in (select reltype from all_relations)
      union
      select
          typ_sch.nspname as handler_schema,
          mtype.typname   as handler_name,
          pro_sch.nspname as target_schema,
          proname         as target_name,
          mtype.typname   as media_type,
          mtype.resolved_media_type
      from pg_proc proc
        join pg_namespace pro_sch on pro_sch.oid = proc.pronamespace
        join media_types mtype on proc.prorettype = mtype.oid
        join pg_namespace typ_sch     on typ_sch.oid = mtype.typnamespace
      where
        pro_sch.nspname = ANY($1) and NOT proretset
       AND prokind = $15 UPDATE "system_metrics" SET "all_logs_logged" = $1, "updated_at" = $2 WHERE "id" = $3 -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:39:08.092Z

set local search_path = '' SELECT * FROM cron.job CREATE INDEX IF NOT EXISTS log_events_4c7f3dd0_52d7_4004_9490_4a8fe5cdd3f4_timestamp_brin_idx ON log_events_4c7f3dd0_52d7_4004_9490_4a8fe5cdd3f4 USING brin (timestamp) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- 创建查询用户完整额度信息的函数
CREATE OR REPLACE FUNCTION get_user_quota_info(
  user_uuid UUID,
  access_password TEXT
) RETURNS JSON AS $$
DECLARE
  user_info RECORD;
  uuid_string TEXT;
  daily_available INTEGER;
  reward_available INTEGER;
BEGIN
  -- 密码校验
  IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
    RAISE EXCEPTION 'Invalid access password';
  END IF;
  
  -- 参数验证
  IF user_uuid IS NULL THEN
    RAISE EXCEPTION 'user_uuid cannot be null';
  END IF;
  
  -- UUID格式验证
  uuid_string := user_uuid::text;
  IF LENGTH(uuid_string) != 36 OR 
     uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid UUID format';
  END IF;
  
  -- 获取用户额度信息
  SELECT 
    daily_free_quota,
    daily_free_used,
    reward_quota,
    reward_used,
    last_free_reset_date,
    is_verified
  INTO user_info
  FROM "membership-look"
  WHERE user_id = user_uuid;
  
  -- 如果用户不存在
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'message', 'User not found'
    );
  END IF;
  
  -- 检查是否需要重置（但不实际重置，只计算）
  IF user_info.last_free_reset_date < CURRENT_DATE THEN
    daily_available := user_info.daily_free_quota;
    reward_available := user_info.reward_quota;
  ELSE
    daily_available := GREATEST(0, user_info.daily_free_quota - user_info.daily_free_used);
    reward_available := GREATEST(0, user_info.reward_quota - user_info.reward_used);
  END IF;
  
  -- 返回完整信息
  RETURN json_build_object(
    'success', true,
    'is_verified', user_info.is_verified,
    'daily_free_quota', user_info.daily_free_quota,
    'daily_free_used', CASE WHEN user_info.last_free_reset_date < CURRENT_DATE THEN 0 ELSE user_info.daily_free_used END,
    'daily_free_remaining', daily_available,
    'reward_quota', user_info.reward_quota,
    'reward_used', CASE WHEN user_info.last_free_reset_date < CURRENT_DATE THEN 0 ELSE user_info.reward_used END,
    'reward_remaining', reward_available,
    'last_reset_date', user_info.last_free_reset_date,
    'needs_reset', user_info.last_free_reset_date < CURRENT_DATE
  );
END;
$$ LANGUAGE plpgsql INSERT INTO "_analytics"."log_events_d880e866_2184_4823_8a8c_f3af54ad1bfd" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16) -- 删除旧的函数
DROP FUNCTION IF EXISTS deduct_daily_free_quota(UUID, INTEGER, TEXT) do $$
begin
  execute
    replace(
      replace(
        replace(
          replace(
            pg_read_file(
'/etc/postgresql-custom/extension-custom-scripts/pg_cron/before-create.sql'            ),
            '@extname@', '''pg_cron'''          ),
          '@extschema@', 'null'        ),
        '@extversion@', 'null'      ),     '@extcascade@', 'false'    );
exception
  when undefined_file then
    -- skip
end
$$ -- 确认所有定时任务已清空
SELECT COUNT(*) as remaining_jobs FROM cron.job with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r update cron.job_run_details set status = $1, start_time = $2 where runid = $3 ROLLBACK COMMENT ON COLUMN "membership-look".reward_used IS '奖励额度已使用量' INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24) -- 创建添加奖励额度的函数
CREATE OR REPLACE FUNCTION add_reward_quota(
  user_uuid UUID,
  reward_amount INTEGER,
  access_password TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  uuid_string TEXT;
BEGIN
  -- 密码校验
  IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
    RAISE EXCEPTION 'Invalid access password';
  END IF;
  
  -- 参数验证
  IF user_uuid IS NULL THEN
    RAISE EXCEPTION 'user_uuid cannot be null';
  END IF;
  
  -- 严格的UUID格式验证
  uuid_string := user_uuid::text;
  IF LENGTH(uuid_string) != 36 OR 
     uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid UUID format';
  END IF;
  
  IF reward_amount IS NULL OR reward_amount <= 0 THEN
    RAISE EXCEPTION 'reward_amount must be positive';
  END IF;
  
  -- 防止恶意大量添加
  IF reward_amount > 50 THEN
    RAISE EXCEPTION 'reward_amount too large (max: 50)';
  END IF;
  
  -- 添加奖励额度
  UPDATE "membership-look"
  SET 
    reward_quota = COALESCE(reward_quota, 0) + reward_amount,
    updated_at = NOW()
  WHERE user_id = user_uuid AND is_verified = TRUE;
  
  -- 检查是否更新成功
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql -- 检查pg_cron是否正在运行
SELECT * FROM cron.job WHERE active = $1 with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r with
      role_setting as (
        select r.rolname, unnest(r.rolconfig) as setting
        from pg_auth_members m
        join pg_roles r on r.oid = m.roleid
        where member = current_user::regrole::oid
      ),
      kv_settings AS (
        SELECT
          rolname,
          substr(setting, $1, strpos(setting, $2) - $3) as key,
          lower(substr(setting, strpos(setting, $4) + $5)) as value
        FROM role_setting
      ),
      iso_setting AS (
        SELECT rolname, value
        FROM kv_settings
        WHERE key = $6
      )
      select
        kv.rolname,
        i.value as iso_lvl,
        coalesce(array_agg(row(kv.key, kv.value)) filter (where key <> $7), $8) as role_settings
      from kv_settings kv
      join pg_settings ps on ps.name = kv.key and (ps.context = $9 or has_parameter_privilege(current_user::regrole::oid, ps.name, $10)) 
      left join iso_setting i on i.rolname = kv.rolname
      group by kv.rolname, i.value BEGIN with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- 检查用户在true表和look表中的存在情况
SELECT $1 as table_name, user_id, email, is_verified, daily_free_quota, daily_free_used, reward_quota, reward_used
FROM "membership-true" 
WHERE user_id = $2
UNION ALL
SELECT $3 as table_name, user_id, email, is_verified, daily_free_quota, daily_free_used, reward_quota, reward_used
FROM "membership-look" 
WHERE user_id = $4 WITH pgrst_source AS (SELECT pgrst_call.pgrst_scalar FROM (SELECT $1 AS json_data) pgrst_payload, LATERAL (SELECT "prompt_id_param", "access_password", "user_email_param" FROM json_to_record(pgrst_payload.json_data) AS _("prompt_id_param" uuid, "access_password" text, "user_email_param" text) LIMIT $2) pgrst_body , LATERAL (SELECT "public"."get_prompt_content"("prompt_id_param" := pgrst_body."prompt_id_param", "access_password" := pgrst_body."access_password", "user_email_param" := pgrst_body."user_email_param") pgrst_scalar) pgrst_call) SELECT $3::bigint AS total_result_set, $4 AS page_total, coalesce(json_agg(_postgrest_t.pgrst_scalar)->$5, $6) AS body, nullif(current_setting($7, $8), $9) AS response_headers, nullif(current_setting($10, $11), $12) AS response_status, $13 AS response_inserted FROM (SELECT "get_prompt_content".* FROM "pgrst_source" AS "get_prompt_content"     ) _postgrest_t SHOW cron.database_name CREATE INDEX IF NOT EXISTS log_events_d880e866_2184_4823_8a8c_f3af54ad1bfd_timestamp_brin_idx ON log_events_d880e866_2184_4823_8a8c_f3af54ad1bfd USING brin (timestamp) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r SELECT 
  p.proname,
  p.prosecdef,
  r.rolname as owner
FROM pg_proc p
JOIN pg_roles r ON p.proowner = r.oid
WHERE p.proname = $1 -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:10:36.368Z

set local search_path = '' WITH    rows AS (      SELECT id      FROM net.http_request_queue      ORDER BY id      LIMIT $1    )    DELETE FROM net.http_request_queue q    USING rows WHERE q.id = rows.id    RETURNING q.id, q.method, q.url, timeout_milliseconds, array(select key || $2 || value from jsonb_each_text(q.headers)), q.body with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r with _base_query as (select * from public."membership-look" order by "membership-look".id asc nulls last limit $1 offset $2)
  select id,case
        when octet_length(email::text) > $3 
        then left(email::text, $4) || $5
        else email::text
      end as email,case
        when octet_length(membership_level::text) > $6 
        then left(membership_level::text, $7) || $8
        else membership_level::text
      end as membership_level,word_count_limit,word_count_used,created_at,updated_at,user_id,is_verified,daily_free_quota,daily_free_used,last_free_reset_date,reward_quota,reward_used from _base_query -- 查看创建的定时任务
SELECT jobid, jobname, schedule, command, active 
FROM cron.job 
WHERE jobname = $1 INSERT INTO "_analytics"."log_events_db2f88a6_2f21_4309_8baf_6ff0b23b88dc" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24) INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8) CREATE EXTENSION IF NOT EXISTS pg_cron BEGIN INSERT INTO "_analytics"."log_events_d880e866_2184_4823_8a8c_f3af54ad1bfd" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112),($113,$114,$115,$116),($117,$118,$119,$120),($121,$122,$123,$124),($125,$126,$127,$128),($129,$130,$131,$132),($133,$134,$135,$136) -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:25:03.796Z

set local search_path = '' SELECT b0."id", b0."name", b0."description", b0."token", b0."type", b0."config_encrypted", b0."user_id", b0."metadata", b0."inserted_at", b0."updated_at" FROM "backends" AS b0 INNER JOIN "rules" AS r1 ON (r1."backend_id" = b0."id") AND (r1."source_id" = $1) WITH pgrst_source AS (SELECT pgrst_call.pgrst_scalar FROM (SELECT $1 AS json_data) pgrst_payload, LATERAL (SELECT "user_uuid", "deduct_amount", "access_password" FROM json_to_record(pgrst_payload.json_data) AS _("user_uuid" uuid, "deduct_amount" integer, "access_password" text) LIMIT $2) pgrst_body , LATERAL (SELECT "public"."deduct_user_word_count"("user_uuid" := pgrst_body."user_uuid", "deduct_amount" := pgrst_body."deduct_amount", "access_password" := pgrst_body."access_password") pgrst_scalar) pgrst_call) SELECT $3::bigint AS total_result_set, $4 AS page_total, coalesce(json_agg(_postgrest_t.pgrst_scalar)->$5, $6) AS body, nullif(current_setting($7, $8), $9) AS response_headers, nullif(current_setting($10, $11), $12) AS response_status, $13 AS response_inserted FROM (SELECT "deduct_user_word_count".* FROM "pgrst_source" AS "deduct_user_word_count"     ) _postgrest_t update public."membership-look" set (daily_free_used) = (select daily_free_used from json_populate_record($1::public."membership-look", $2)) where id = $3 update public."membership-look" set (reward_used) = (select reward_used from json_populate_record($1::public."membership-look", $2)) where id = $3 -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:16:35.121Z

set local search_path = '' with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:48:11.276Z

set local search_path = '' SELECT count(*) FROM "sources" AS s0 WHERE (s0."user_id" = $1) INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112) DELETE FROM "extensions" AS e0 WHERE (e0."tenant_external_id" = $1) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r update public."membership-true" set (daily_free_used) = (select daily_free_used from json_populate_record($1::public."membership-true", $2)) where id = $3 update public."membership-look" set (reward_quota) = (select reward_quota from json_populate_record($1::public."membership-look", $2)) where id = $3 INSERT INTO "_analytics"."log_events_db2f88a6_2f21_4309_8baf_6ff0b23b88dc" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112),($113,$114,$115,$116),($117,$118,$119,$120),($121,$122,$123,$124),($125,$126,$127,$128),($129,$130,$131,$132),($133,$134,$135,$136),($137,$138,$139,$140),($141,$142,$143,$144),($145,$146,$147,$148),($149,$150,$151,$152),($153,$154,$155,$156),($157,$158,$159,$160),($161,$162,$163,$164),($165,$166,$167,$168),($169,$170,$171,$172),($173,$174,$175,$176),($177,$178,$179,$180),($181,$182,$183,$184),($185,$186,$187,$188) SELECT prosrc FROM pg_proc WHERE proname = $1 -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:13:07.601Z

set local search_path = '' SELECT * FROM "membership-look" WHERE user_id = $1 BEGIN -- 查看是否有触发器
SELECT trigger_name, event_manipulation, event_object_table, action_statement
FROM information_schema.triggers 
WHERE event_object_schema = $1 
AND (event_object_table = $2 OR event_object_table = $3) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r SELECT * FROM pg_extension WHERE extname = $1 update public."membership-true" set (reward_quota) = (select reward_quota from json_populate_record($1::public."membership-true", $2)) where id = $3 -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:46:54.856Z

set local search_path = '' WITH
      role_setting AS (
        SELECT setdatabase as database,
               unnest(setconfig) as setting
        FROM pg_catalog.pg_db_role_setting
        WHERE setrole = CURRENT_USER::regrole::oid
          AND setdatabase IN ($2, (SELECT oid FROM pg_catalog.pg_database WHERE datname = CURRENT_CATALOG))
      ),
      kv_settings AS (
        SELECT database,
               substr(setting, $3, strpos(setting, $4) - $5) as k,
               substr(setting, strpos(setting, $6) + $7) as v
        FROM role_setting
        
      )
      SELECT DISTINCT ON (key)
             replace(k, $8, $9) AS key,
             v AS value
      FROM kv_settings
      WHERE k = ANY($1) AND v IS NOT NULL
      ORDER BY key, database DESC NULLS LAST SET statement_timeout TO '12h' SELECT * FROM cron.job LIMIT $1 WITH pgrst_source AS ( SELECT "public"."membership-look"."is_verified", "public"."membership-look"."daily_free_quota", "public"."membership-look"."daily_free_used", "public"."membership-look"."reward_quota", "public"."membership-look"."reward_used", "public"."membership-look"."last_free_reset_date" FROM "public"."membership-look"  WHERE  "public"."membership-look"."user_id" = $1    )  SELECT $2::bigint AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, coalesce(json_agg(_postgrest_t)->$3, $4) AS body, nullif(current_setting($5, $6), $7) AS response_headers, nullif(current_setting($8, $9), $10) AS response_status, $11 AS response_inserted FROM ( SELECT * FROM pgrst_source ) _postgrest_t with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- 更新触发器函数以包含奖励额度字段
CREATE OR REPLACE FUNCTION sync_membership_to_look()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    INSERT INTO public."membership-look" VALUES (NEW.*)
    ON CONFLICT (id) DO NOTHING;
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    UPDATE public."membership-look" SET 
      email = NEW.email,
      membership_level = NEW.membership_level,
      word_count_limit = NEW.word_count_limit,
      word_count_used = NEW.word_count_used,
      created_at = NEW.created_at,
      updated_at = NEW.updated_at,
      user_id = NEW.user_id,
      is_verified = NEW.is_verified,
      daily_free_quota = NEW.daily_free_quota,
      daily_free_used = NEW.daily_free_used,
      last_free_reset_date = NEW.last_free_reset_date,
      reward_quota = NEW.reward_quota,
      reward_used = NEW.reward_used
    WHERE id = NEW.id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    DELETE FROM public."membership-look" WHERE id = OLD.id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:09:50.610Z

set local search_path = '' with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- source: dashboard
-- user: self host
-- date: 2025-08-02T10:47:02.472Z

set local search_path = '' SELECT
  e.name,
  n.nspname AS schema,
  e.default_version,
  x.extversion AS installed_version,
  e.comment
FROM
  pg_available_extensions() e(name, default_version, comment)
  LEFT JOIN pg_extension x ON e.name = x.extname
  LEFT JOIN pg_namespace n ON x.extnamespace = n.oid with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r COMMIT -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:10:27.173Z

set local search_path = '' WITH pgrst_source AS ( SELECT "public"."membership-look"."word_count_used", "public"."membership-look"."word_count_limit", "public"."membership-look"."membership_level" FROM "public"."membership-look"  WHERE  "public"."membership-look"."user_id" = $1    )  SELECT $2::bigint AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, coalesce(json_agg(_postgrest_t)->$3, $4) AS body, nullif(current_setting($5, $6), $7) AS response_headers, nullif(current_setting($8, $9), $10) AS response_status, $11 AS response_inserted FROM ( SELECT * FROM pgrst_source ) _postgrest_t -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:11:22.336Z

set local search_path = '' with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r SELECT * FROM "membership-true" WHERE user_id = $1 INSERT INTO "_analytics"."log_events_b8f6e455_ebb0_4da3_99e7_b9d66629c2a4" ("id","timestamp","body","event_message") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12),($13,$14,$15,$16),($17,$18,$19,$20),($21,$22,$23,$24),($25,$26,$27,$28),($29,$30,$31,$32),($33,$34,$35,$36),($37,$38,$39,$40),($41,$42,$43,$44),($45,$46,$47,$48),($49,$50,$51,$52),($53,$54,$55,$56),($57,$58,$59,$60),($61,$62,$63,$64),($65,$66,$67,$68),($69,$70,$71,$72),($73,$74,$75,$76),($77,$78,$79,$80),($81,$82,$83,$84),($85,$86,$87,$88),($89,$90,$91,$92),($93,$94,$95,$96),($97,$98,$99,$100),($101,$102,$103,$104),($105,$106,$107,$108),($109,$110,$111,$112),($113,$114,$115,$116),($117,$118,$119,$120),($121,$122,$123,$124),($125,$126,$127,$128),($129,$130,$131,$132),($133,$134,$135,$136),($137,$138,$139,$140),($141,$142,$143,$144),($145,$146,$147,$148),($149,$150,$151,$152),($153,$154,$155,$156),($157,$158,$159,$160),($161,$162,$163,$164),($165,$166,$167,$168),($169,$170,$171,$172),($173,$174,$175,$176),($177,$178,$179,$180),($181,$182,$183,$184),($185,$186,$187,$188),($189,$190,$191,$192),($193,$194,$195,$196),($197,$198,$199,$200),($201,$202,$203,$204),($205,$206,$207,$208),($209,$210,$211,$212),($213,$214,$215,$216),($217,$218,$219,$220),($221,$222,$223,$224),($225,$226,$227,$228),($229,$230,$231,$232) CREATE TABLE IF NOT EXISTS log_events_d880e866_2184_4823_8a8c_f3af54ad1bfd (
  id TEXT PRIMARY KEY,
  body JSONB,
  event_message TEXT,
  timestamp TIMESTAMP
) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:38:37.204Z

set local search_path = '' SELECT s0."id", s0."name", s0."service_name", s0."token", s0."public_token", s0."favorite", s0."bigquery_table_ttl", s0."api_quota", s0."webhook_notification_url", s0."slack_hook_url", s0."bq_table_partition_type", s0."custom_event_message_keys", s0."log_events_updated_at", s0."notifications_every", s0."lock_schema", s0."validate_schema", s0."drop_lql_filters", s0."drop_lql_string", s0."v2_pipeline", s0."disable_tailing", s0."suggested_keys", s0."transform_copy_fields", s0."user_id", s0."notifications", s0."inserted_at", s0."updated_at", s0."user_id" FROM "sources" AS s0 WHERE (s0."user_id" = $1) ORDER BY s0."user_id" CREATE TABLE IF NOT EXISTS "schema_migrations" ("version" bigint, "inserted_at" timestamp(0), PRIMARY KEY ("version")) -- 测试新的数据库函数
SELECT get_user_quota_info(
  $1::UUID,
  $2
) with records as (
  select
    c.oid::int8 as "id",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as "sql"
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) "data"
from records r -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:24:59.270Z

set local search_path = '' SELECT column_name, data_type FROM information_schema.columns WHERE table_name = $1 AND column_name = $2 SELECT 
  p.proname,
  p.prosecdef,
  pg_get_functiondef(p.oid) as function_definition
FROM pg_proc p
WHERE p.proname = $1 -- Adapted from information_schema.schemata

select
  n.oid as id,
  n.nspname as name,
  u.rolname as owner
from
  pg_namespace n,
  pg_roles u
where
  n.nspowner = u.oid
  and (
    pg_has_role(n.nspowner, $1)
    or has_schema_privilege(n.oid, $2)
  )
  and not pg_catalog.starts_with(n.nspname, $3)
  and not pg_catalog.starts_with(n.nspname, $4)
 and not (n.nspname in ($5,$6,$7)) SELECT * FROM pg_available_extensions WHERE name = $1 BEGIN ISOLATION LEVEL READ COMMITTED READ ONLY update cron.job_run_details set status = $1, return_message = $2, end_time = $3 where runid = $4 INSERT INTO "sessions" ("aal", "created_at", "factor_id", "id", "ip", "not_after", "refreshed_at", "tag", "updated_at", "user_agent", "user_id") VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11) select (select count(*) from public."propmt-zhanshi"), $1 as is_estimate CREATE OR REPLACE FUNCTION pg_temp.pg_get_tabledef(in_schema varchar,in_table varchar,_verbose boolean,VARIADIC arr pg_temp.tabledefs[]DEFAULT'{}'::pg_temp.tabledefs[])RETURNS text LANGUAGE plpgsql VOLATILE AS $$ DECLARE v_qualified text:='';v_table_ddl text;v_table_oid int;v_colrec record;v_constraintrec record;v_trigrec record;v_indexrec record;v_rec record;v_constraint_name text;v_constraint_def text;v_pkey_def text:='';v_fkey_def text:='';v_fkey_defs text:='';v_trigger text:='';v_partition_key text:='';v_partbound text;v_parent text;v_parent_schema text;v_persist text;v_temp text:='';v_temp2 text;v_relopts text;v_tablespace text;v_pgversion int;bSerial boolean;bPartition boolean;bInheritance boolean;bRelispartition boolean;constraintarr text[]:='{}';constraintelement text;bSkip boolean;bVerbose boolean:=False;v_cnt1 integer;v_cnt2 integer;search_path_old text:='';search_path_new text:='';v_partial boolean;v_pos integer;pkcnt int:=0;fkcnt int:=0;trigcnt int:=0;cmtcnt int:=0;pktype pg_temp.tabledefs:='PKEY_INTERNAL';fktype pg_temp.tabledefs:='FKEYS_INTERNAL';trigtype pg_temp.tabledefs:='NO_TRIGGERS';arglen integer;vargs text;avarg pg_temp.tabledefs;v_ret text;v_diag1 text;v_diag2 text;v_diag3 text;v_diag4 text;v_diag5 text;v_diag6 text;BEGIN SET client_min_messages='notice';IF _verbose THEN bVerbose=True;END IF;arglen:=array_length($4,1);IF arglen IS NULL THEN NULL;ELSE IF bVerbose THEN RAISE NOTICE'arguments=%',$4;END IF;FOREACH avarg IN ARRAY $4 LOOP IF bVerbose THEN RAISE NOTICE'arg=%',avarg;END IF;IF avarg='FKEYS_INTERNAL'OR avarg='FKEYS_EXTERNAL'OR avarg='FKEYS_NONE'THEN fkcnt=fkcnt+1;fktype=avarg;ELSEIF avarg='INCLUDE_TRIGGERS'OR avarg='NO_TRIGGERS'THEN trigcnt=trigcnt+1;trigtype=avarg;ELSEIF avarg='PKEY_EXTERNAL'THEN pkcnt=pkcnt+1;pktype=avarg;ELSEIF avarg='COMMENTS'THEN cmtcnt=cmtcnt+1;END IF;END LOOP;IF fkcnt>1 THEN RAISE WARNING'Only one foreign key option can be provided. You provided %',fkcnt;RETURN'';ELSEIF trigcnt>1 THEN RAISE WARNING'Only one trigger option can be provided. You provided %',trigcnt;RETURN'';ELSEIF pkcnt>1 THEN RAISE WARNING'Only one pkey option can be provided. You provided %',pkcnt;RETURN'';ELSEIF cmtcnt>1 THEN RAISE WARNING'Only one comments option can be provided. You provided %',cmtcnt;RETURN'';END IF;END IF;SELECT c.oid,(select setting from pg_settings where name='server_version_num')INTO v_table_oid,v_pgversion FROM pg_catalog.pg_class c LEFT JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace WHERE c.relkind in('r','p')AND c.relname=in_table AND n.nspname=in_schema;SELECT setting INTO search_path_old FROM pg_settings WHERE name='search_path';EXECUTE'SET search_path = "public"';SELECT setting INTO search_path_new FROM pg_settings WHERE name='search_path';IF(v_table_oid IS NULL)THEN RAISE EXCEPTION'table does not exist';END IF;SELECT tablespace INTO v_temp FROM pg_tables WHERE schemaname=in_schema and tablename=in_table and tablespace IS NOT NULL;IF v_temp IS NULL THEN v_tablespace:='TABLESPACE pg_default';ELSE v_tablespace:='TABLESPACE '||v_temp;END IF;WITH relopts AS(SELECT unnest(c.reloptions)relopts FROM pg_class c,pg_namespace n WHERE n.nspname=in_schema and n.oid=c.relnamespace and c.relname=in_table)SELECT string_agg(r.relopts,', ')as relopts INTO v_temp from relopts r;IF v_temp IS NULL THEN v_relopts:='';ELSE v_relopts:=' WITH ('||v_temp||')';END IF;v_partbound:='';bPartition:=False;bInheritance:=False;IF v_pgversion<100000 THEN SELECT c2.relname parent,c2.relnamespace::regnamespace INTO v_parent,v_parent_schema from pg_class c1,pg_namespace n,pg_inherits i,pg_class c2 WHERE n.nspname=in_schema and n.oid=c1.relnamespace and c1.relname=in_table and c1.oid=i.inhrelid and i.inhparent=c2.oid and c1.relkind='r';IF(v_parent IS NOT NULL)THEN bPartition:=True;bInheritance:=True;END IF;ELSE SELECT c2.relname parent,c1.relispartition,pg_get_expr(c1.relpartbound,c1.oid,true),c2.relnamespace::regnamespace INTO v_parent,bRelispartition,v_partbound,v_parent_schema from pg_class c1,pg_namespace n,pg_inherits i,pg_class c2 WHERE n.nspname=in_schema and n.oid=c1.relnamespace and c1.relname=in_table and c1.oid=i.inhrelid and i.inhparent=c2.oid and c1.relkind='r';IF(v_parent IS NOT NULL)THEN bPartition:=True;IF bRelispartition THEN bInheritance:=False;ELSE bInheritance:=True;END IF;END IF;END IF;IF bPartition THEN SELECT count(*)INTO v_cnt1 FROM information_schema.tables t WHERE EXISTS(SELECT REGEXP_MATCHES(s.table_name,'([A-Z]+)','g')FROM information_schema.tables s WHERE t.table_schema=s.table_schema AND t.table_name=s.table_name AND t.table_schema=in_schema AND t.table_name=in_table AND t.table_type='BASE TABLE');SELECT COUNT(*)INTO v_cnt2 FROM pg_get_keywords()WHERE word=in_table AND catcode='R';IF bInheritance THEN IF v_cnt1>0 OR v_cnt2>0 THEN v_table_ddl:='CREATE TABLE '||in_schema||'."'||in_table||'"( '||E'\n';ELSE v_table_ddl:='CREATE TABLE '||in_schema||'.'||in_table||'( '||E'\n';END IF;ELSE IF v_relopts<>''THEN IF v_cnt1>0 OR v_cnt2>0 THEN v_table_ddl:='CREATE TABLE '||in_schema||'."'||in_table||'" PARTITION OF '||in_schema||'.'||v_parent||' '||v_partbound||v_relopts||' '||v_tablespace||'; '||E'\n';ELSE v_table_ddl:='CREATE TABLE '||in_schema||'.'||in_table||' PARTITION OF '||in_schema||'.'||v_parent||' '||v_partbound||v_relopts||' '||v_tablespace||'; '||E'\n';END IF;ELSE IF v_cnt1>0 OR v_cnt2>0 THEN v_table_ddl:='CREATE TABLE '||in_schema||'."'||in_table||'" PARTITION OF '||in_schema||'.'||v_parent||' '||v_partbound||' '||v_tablespace||'; '||E'\n';ELSE v_table_ddl:='CREATE TABLE '||in_schema||'.'||in_table||' PARTITION OF '||in_schema||'.'||v_parent||' '||v_partbound||' '||v_tablespace||'; '||E'\n';END IF;END IF;END IF;END IF;IF bVerbose THEN RAISE NOTICE'(1)tabledef so far: %',v_table_ddl;END IF;IF NOT bPartition THEN select c.relpersistence into v_persist from pg_class c,pg_namespace n where n.nspname=in_schema and n.oid=c.relnamespace and c.relname=in_table and c.relkind='r';IF v_persist='u'THEN v_temp:='UNLOGGED';ELSIF v_persist='t'THEN v_temp:='TEMPORARY';ELSE v_temp:='';END IF;END IF;IF NOT bPartition THEN SELECT count(*)INTO v_cnt1 FROM information_schema.tables t WHERE EXISTS(SELECT REGEXP_MATCHES(s.table_name,'([A-Z]+)','g')FROM information_schema.tables s WHERE t.table_schema=s.table_schema AND t.table_name=s.table_name AND t.table_schema=in_schema AND t.table_name=in_table AND t.table_type='BASE TABLE');IF v_cnt1>0 THEN v_table_ddl:='CREATE '||v_temp||' TABLE '||in_schema||'."'||in_table||'" ('||E'\n';ELSE v_table_ddl:='CREATE '||v_temp||' TABLE '||in_schema||'.'||in_table||' ('||E'\n';END IF;END IF;IF NOT bPartition THEN FOR v_colrec IN SELECT c.column_name,c.data_type,c.udt_name,c.udt_schema,c.character_maximum_length,c.is_nullable,c.column_default,c.numeric_precision,c.numeric_scale,c.is_identity,c.identity_generation,c.is_generated,c.generation_expression FROM information_schema.columns c WHERE(table_schema,table_name)=(in_schema,in_table)ORDER BY ordinal_position LOOP IF bVerbose THEN RAISE NOTICE'(col loop) name=%  type=%  udt_name=%  default=%  is_generated=%  gen_expr=%',v_colrec.column_name,v_colrec.data_type,v_colrec.udt_name,v_colrec.column_default,v_colrec.is_generated,v_colrec.generation_expression;END IF;SELECT CASE WHEN pg_get_serial_sequence(quote_ident(in_schema)||'.'||quote_ident(in_table),v_colrec.column_name)IS NOT NULL THEN True ELSE False END into bSerial;IF bVerbose THEN SELECT pg_get_serial_sequence(quote_ident(in_schema)||'.'||quote_ident(in_table),v_colrec.column_name)into v_temp;IF v_temp IS NULL THEN v_temp='NA';END IF;SELECT pg_temp.pg_get_coldef(in_schema,in_table,v_colrec.column_name)INTO v_diag1;RAISE NOTICE'DEBUG table: %  Column: %  datatype: %  Serial=%  serialval=%  coldef=%',v_qualified,v_colrec.column_name,v_colrec.data_type,bSerial,v_temp,v_diag1;RAISE NOTICE'DEBUG tabledef: %',v_table_ddl;END IF;SELECT COUNT(*)INTO v_cnt1 FROM information_schema.columns t WHERE EXISTS(SELECT REGEXP_MATCHES(s.column_name,'([A-Z]+)','g')FROM information_schema.columns s WHERE t.table_schema=s.table_schema and t.table_name=s.table_name and t.column_name=s.column_name AND t.table_schema=quote_ident(in_schema)AND column_name=v_colrec.column_name);SELECT COUNT(*)INTO v_cnt2 FROM pg_get_keywords()WHERE word=v_colrec.column_name AND catcode='R';IF v_cnt1>0 OR v_cnt2>0 THEN v_table_ddl:=v_table_ddl||'  "'||v_colrec.column_name||'" ';ELSE v_table_ddl:=v_table_ddl||'  '||v_colrec.column_name||' ';END IF;IF v_colrec.is_generated='ALWAYS'and v_colrec.generation_expression IS NOT NULL THEN v_temp=v_colrec.data_type||' GENERATED ALWAYS AS ('||v_colrec.generation_expression||') STORED ';ELSEIF v_colrec.udt_name in('geometry','box2d','box2df','box3d','geography','geometry_dump','gidx','spheroid','valid_detail')THEN v_temp=v_colrec.udt_name;ELSEIF v_colrec.data_type='USER-DEFINED'THEN v_temp=v_colrec.udt_schema||'.'||v_colrec.udt_name;ELSEIF v_colrec.data_type='ARRAY'THEN v_temp=pg_temp.pg_get_coldef(in_schema,in_table,v_colrec.column_name);ELSEIF pg_get_serial_sequence(quote_ident(in_schema)||'.'||quote_ident(in_table),v_colrec.column_name)IS NOT NULL THEN v_temp=pg_temp.pg_get_coldef(in_schema,in_table,v_colrec.column_name);ELSE v_temp=v_colrec.data_type;END IF;IF v_colrec.is_identity='YES'THEN IF v_colrec.identity_generation='ALWAYS'THEN v_temp=v_temp||' GENERATED ALWAYS AS IDENTITY';ELSE v_temp=v_temp||' GENERATED BY DEFAULT AS IDENTITY';END IF;ELSEIF v_colrec.character_maximum_length IS NOT NULL THEN v_temp=v_temp||('('||v_colrec.character_maximum_length||')');ELSEIF v_colrec.numeric_precision>0 AND v_colrec.numeric_scale>0 THEN v_temp=v_temp||'('||v_colrec.numeric_precision||','||v_colrec.numeric_scale||')';END IF;IF bSerial THEN v_temp=v_temp||' NOT NULL';ELSEIF v_colrec.is_nullable='NO'THEN v_temp=v_temp||' NOT NULL';ELSEIF v_colrec.is_nullable='YES'THEN v_temp=v_temp||' NULL';END IF;IF v_colrec.column_default IS NOT null AND NOT bSerial THEN v_temp=v_temp||(' DEFAULT '||v_colrec.column_default);END IF;v_temp=v_temp||','||E'\n';v_table_ddl:=v_table_ddl||v_temp;END LOOP;END IF;IF bVerbose THEN RAISE NOTICE'(2)tabledef so far: %',v_table_ddl;END IF;IF v_pgversion<110000 THEN FOR v_constraintrec IN SELECT con.conname as constraint_name,con.contype as constraint_type,CASE WHEN con.contype='p'THEN 1 WHEN con.contype='u'THEN 2 WHEN con.contype='f'THEN 3 WHEN con.contype='c'THEN 4 ELSE 5 END as type_rank,pg_get_constraintdef(con.oid)as constraint_definition FROM pg_catalog.pg_constraint con JOIN pg_catalog.pg_class rel ON rel.oid=con.conrelid JOIN pg_catalog.pg_namespace nsp ON nsp.oid=connamespace WHERE nsp.nspname=in_schema AND rel.relname=in_table ORDER BY type_rank LOOP v_constraint_name:=v_constraintrec.constraint_name;v_constraint_def:=v_constraintrec.constraint_definition;IF v_constraintrec.type_rank=1 THEN IF pkcnt=0 OR pktype='PKEY_INTERNAL'THEN v_constraint_name:=v_constraintrec.constraint_name;v_constraint_def:=v_constraintrec.constraint_definition;v_table_ddl:=v_table_ddl||'  '||'CONSTRAINT'||' '||v_constraint_name||' '||v_constraint_def||','||E'\n';ELSE SELECT'ALTER TABLE ONLY '||in_schema||'.'||c.relname||' ADD CONSTRAINT '||r.conname||' '||pg_catalog.pg_get_constraintdef(r.oid,true)||';'INTO v_pkey_def FROM pg_catalog.pg_constraint r,pg_class c,pg_namespace n where r.conrelid=c.oid and r.contype='p'and n.oid=r.connamespace and n.nspname=in_schema AND c.relname=in_table and r.conname=v_constraint_name;END IF;IF bPartition THEN continue;END IF;ELSIF v_constraintrec.type_rank=3 THEN IF fktype='FKEYS_NONE'THEN continue;ELSIF fkcnt=0 OR fktype='FKEYS_INTERNAL'THEN v_table_ddl:=v_table_ddl||'  '||'CONSTRAINT'||' '||v_constraint_name||' '||v_constraint_def||','||E'\n';ELSE SELECT'ALTER TABLE ONLY '||n.nspname||'.'||c2.relname||' ADD CONSTRAINT '||r.conname||' '||pg_catalog.pg_get_constraintdef(r.oid,true)||';'INTO v_fkey_def FROM pg_constraint r,pg_class c1,pg_namespace n,pg_class c2 where r.conrelid=c1.oid and r.contype='f'and n.nspname=in_schema and n.oid=r.connamespace and r.conrelid=c2.oid and c2.relname=in_table;v_fkey_defs=v_fkey_defs||v_fkey_def||E'\n';END IF;ELSE v_table_ddl:=v_table_ddl||'  '||'CONSTRAINT'||' '||v_constraint_name||' '||v_constraint_def||','||E'\n';END IF;if bVerbose THEN RAISE NOTICE'DEBUG4: constraint name=% constraint_def=%',v_constraint_name,v_constraint_def;END IF;constraintarr:=constraintarr||v_constraintrec.constraint_name::text;END LOOP;ELSE FOR v_constraintrec IN SELECT con.conname as constraint_name,con.contype as constraint_type,CASE WHEN con.contype='p'THEN 1 WHEN con.contype='u'THEN 2 WHEN con.contype='f'THEN 3 WHEN con.contype='c'THEN 4 ELSE 5 END as type_rank,pg_get_constraintdef(con.oid)as constraint_definition FROM pg_catalog.pg_constraint con JOIN pg_catalog.pg_class rel ON rel.oid=con.conrelid JOIN pg_catalog.pg_namespace nsp ON nsp.oid=connamespace WHERE nsp.nspname=in_schema AND rel.relname=in_table AND con.conparentid=0 ORDER BY type_rank LOOP v_constraint_name:=v_constraintrec.constraint_name;v_constraint_def:=v_constraintrec.constraint_definition;IF v_constraintrec.type_rank=1 THEN IF pkcnt=0 OR pktype='PKEY_INTERNAL'THEN v_constraint_name:=v_constraintrec.constraint_name;v_constraint_def:=v_constraintrec.constraint_definition;v_table_ddl:=v_table_ddl||'  '||'CONSTRAINT'||' '||v_constraint_name||' '||v_constraint_def||','||E'\n';ELSE SELECT'ALTER TABLE ONLY '||in_schema||'.'||c.relname||' ADD CONSTRAINT '||r.conname||' '||pg_catalog.pg_get_constraintdef(r.oid,true)||';'INTO v_pkey_def FROM pg_catalog.pg_constraint r,pg_class c,pg_namespace n where r.conrelid=c.oid and r.contype='p'and n.oid=r.connamespace and n.nspname=in_schema AND c.relname=in_table;END IF;IF bPartition THEN continue;END IF;ELSIF v_constraintrec.type_rank=3 THEN IF fktype='FKEYS_NONE'THEN continue;ELSIF fkcnt=0 OR fktype='FKEYS_INTERNAL'THEN v_table_ddl:=v_table_ddl||'  '||'CONSTRAINT'||' '||v_constraint_name||' '||v_constraint_def||','||E'\n';ELSE SELECT'ALTER TABLE ONLY '||n.nspname||'.'||c2.relname||' ADD CONSTRAINT '||r.conname||' '||pg_catalog.pg_get_constraintdef(r.oid,true)||';'INTO v_fkey_def FROM pg_constraint r,pg_class c1,pg_namespace n,pg_class c2 where r.conrelid=c1.oid and r.contype='f'and n.nspname=in_schema and n.oid=r.connamespace and r.conrelid=c2.oid and c2.relname=in_table and r.conname=v_constraint_name and r.conparentid=0;v_fkey_defs=v_fkey_defs||v_fkey_def||E'\n';END IF;ELSE v_table_ddl:=v_table_ddl||'  '||'CONSTRAINT'||' '||v_constraint_name||' '||v_constraint_def||','||E'\n';END IF;if bVerbose THEN RAISE NOTICE'DEBUG4: constraint name=% constraint_def=%',v_constraint_name,v_constraint_def;END IF;constraintarr:=constraintarr||v_constraintrec.constraint_name::text;END LOOP;END IF;select substring(v_table_ddl,length(v_table_ddl)-1,1)INTO v_temp;IF v_temp=','THEN v_table_ddl=substr(v_table_ddl,0,length(v_table_ddl)-1)||E'\n';END IF;IF bVerbose THEN RAISE NOTICE'(3)tabledef so far: %',trim(v_table_ddl);END IF;IF bVerbose THEN RAISE NOTICE'(4)tabledef so far: %',v_table_ddl;END IF;IF bPartition and bInheritance THEN IF v_parent_schema=''OR v_parent_schema IS NULL THEN v_parent_schema=in_schema;END IF;v_table_ddl:=v_table_ddl||') INHERITS ('||v_parent_schema||'.'||v_parent||') '||E'\n'||v_relopts||' '||v_tablespace||';'||E'\n';END IF;IF v_pgversion>=100000 AND NOT bPartition and NOT bInheritance THEN SELECT pg_get_partkeydef(c1.oid)as partition_key INTO v_partition_key FROM pg_class c1 JOIN pg_namespace n ON(n.oid=c1.relnamespace)LEFT JOIN pg_partitioned_table p ON(c1.oid=p.partrelid)WHERE n.nspname=in_schema and n.oid=c1.relnamespace and c1.relname=in_table and c1.relkind='p';IF v_partition_key IS NOT NULL AND v_partition_key<>''THEN v_table_ddl:=v_table_ddl||') PARTITION BY '||v_partition_key||';'||E'\n';ELSEIF v_relopts<>''THEN v_table_ddl:=v_table_ddl||') '||v_relopts||' '||v_tablespace||';'||E'\n';ELSE v_table_ddl:=v_table_ddl||') '||v_tablespace||';'||E'\n';END IF;END IF;IF bVerbose THEN RAISE NOTICE'(5)tabledef so far: %',v_table_ddl;END IF;IF v_pkey_def<>''THEN v_table_ddl:=v_table_ddl||v_pkey_def||E'\n';END IF;IF v_fkey_defs<>''THEN v_table_ddl:=v_table_ddl||v_fkey_defs||E'\n';END IF;IF bVerbose THEN RAISE NOTICE'(6)tabledef so far: %',v_table_ddl;END IF;FOR v_indexrec IN SELECT indexdef,COALESCE(tablespace,'pg_default')as tablespace,indexname FROM pg_indexes WHERE(schemaname,tablename)=(in_schema,in_table)LOOP bSkip=False;FOREACH constraintelement IN ARRAY constraintarr LOOP IF constraintelement=v_indexrec.indexname THEN bSkip=True;EXIT;END IF;END LOOP;if bSkip THEN CONTINUE;END IF;v_indexrec.indexdef:=REPLACE(v_indexrec.indexdef,'CREATE INDEX','CREATE INDEX IF NOT EXISTS');v_indexrec.indexdef:=REPLACE(v_indexrec.indexdef,'CREATE UNIQUE INDEX','CREATE UNIQUE INDEX IF NOT EXISTS');IF v_partition_key IS NOT NULL AND v_partition_key<>''THEN v_table_ddl:=v_table_ddl||v_indexrec.indexdef||';'||E'\n';ELSE select CASE WHEN i.indpred IS NOT NULL THEN True ELSE False END INTO v_partial FROM pg_index i JOIN pg_class c1 ON(i.indexrelid=c1.oid)JOIN pg_class c2 ON(i.indrelid=c2.oid)WHERE c1.relnamespace::regnamespace::text=in_schema AND c2.relnamespace::regnamespace::text=in_schema AND c2.relname=in_table AND c1.relname=v_indexrec.indexname;IF v_partial THEN v_temp=v_indexrec.indexdef;v_pos=POSITION(' WHERE 'IN v_temp);v_temp2=SUBSTRING(v_temp,v_pos);v_temp=SUBSTRING(v_temp,1,v_pos);v_table_ddl:=v_table_ddl||v_temp||' TABLESPACE '||v_indexrec.tablespace||v_temp2||';'||E'\n';ELSE v_table_ddl:=v_table_ddl||v_indexrec.indexdef||' TABLESPACE '||v_indexrec.tablespace||';'||E'\n';END IF;END IF;END LOOP;IF bVerbose THEN RAISE NOTICE'(7)tabledef so far: %',v_table_ddl;END IF;IF cmtcnt>0 THEN FOR v_rec IN SELECT c.relname,'COMMENT ON '||CASE WHEN c.relkind in('r','p')AND a.attname IS NULL THEN'TABLE 'WHEN c.relkind in('r','p')AND a.attname IS NOT NULL THEN'COLUMN 'WHEN c.relkind='f'THEN'FOREIGN TABLE 'WHEN c.relkind='m'THEN'MATERIALIZED VIEW 'WHEN c.relkind='v'THEN'VIEW 'WHEN c.relkind='i'THEN'INDEX 'WHEN c.relkind='S'THEN'SEQUENCE 'ELSE'XX'END||n.nspname||'.'||CASE WHEN c.relkind in('r','p')AND a.attname IS NOT NULL THEN quote_ident(c.relname)||'.'||a.attname ELSE quote_ident(c.relname)END||' IS '||quote_literal(d.description)||';'as ddl FROM pg_class c JOIN pg_namespace n ON(n.oid=c.relnamespace)LEFT JOIN pg_description d ON(c.oid=d.objoid)LEFT JOIN pg_attribute a ON(c.oid=a.attrelid AND a.attnum>0 and a.attnum=d.objsubid)WHERE d.description IS NOT NULL AND n.nspname=in_schema AND c.relname=in_table ORDER BY 2 desc,ddl LOOP v_table_ddl=v_table_ddl||v_rec.ddl||E'\n';END LOOP;END IF;IF bVerbose THEN RAISE NOTICE'(8)tabledef so far: %',v_table_ddl;END IF;IF trigtype='INCLUDE_TRIGGERS'THEN FOR v_trigrec IN select pg_get_triggerdef(t.oid,True)||';'as triggerdef FROM pg_trigger t,pg_class c,pg_namespace n WHERE n.nspname=in_schema and n.oid=c.relnamespace and c.relname=in_table and c.relkind='r'and t.tgrelid=c.oid and NOT t.tgisinternal LOOP v_table_ddl:=v_table_ddl||v_trigrec.triggerdef;v_table_ddl:=v_table_ddl||E'\n';IF bVerbose THEN RAISE NOTICE'triggerdef = %',v_trigrec.triggerdef;END IF;END LOOP;END IF;IF bVerbose THEN RAISE NOTICE'(9)tabledef so far: %',v_table_ddl;END IF;v_table_ddl:=v_table_ddl||E'\n';IF bVerbose THEN RAISE NOTICE'(10)tabledef so far: %',v_table_ddl;END IF;IF search_path_old=''THEN SELECT set_config('search_path','',false)into v_temp;ELSE EXECUTE'SET search_path = '||search_path_old;END IF;RETURN v_table_ddl;EXCEPTION WHEN others THEN BEGIN GET STACKED DIAGNOSTICS v_diag1=MESSAGE_TEXT,v_diag2=PG_EXCEPTION_DETAIL,v_diag3=PG_EXCEPTION_HINT,v_diag4=RETURNED_SQLSTATE,v_diag5=PG_CONTEXT,v_diag6=PG_EXCEPTION_CONTEXT;v_ret:='line='||v_diag6||'. '||v_diag4||'. '||v_diag1;RAISE EXCEPTION'%',v_ret;RETURN'';END;END;$$ SELECT users.aud, users.banned_until, users.confirmation_sent_at, users.confirmation_token, users.confirmed_at, users.created_at, users.deleted_at, users.email, users.email_change, users.email_change_confirm_status, users.email_change_sent_at, users.email_change_token_current, users.email_change_token_new, users.email_confirmed_at, users.encrypted_password, users.id, users.instance_id, users.invited_at, users.is_anonymous, users.is_sso_user, users.last_sign_in_at, users.phone, users.phone_change, users.phone_change_sent_at, users.phone_change_token, users.phone_confirmed_at, users.raw_app_meta_data, users.raw_user_meta_data, users.reauthentication_sent_at, users.reauthentication_token, users.recovery_sent_at, users.recovery_token, users.role, users.updated_at FROM users AS users WHERE instance_id = $1 and LOWER(email) = $2 and aud = $3 and is_sso_user = $4 LIMIT $5 SELECT s0."id", s0."name", s0."service_name", s0."token", s0."public_token", s0."favorite", s0."bigquery_table_ttl", s0."api_quota", s0."webhook_notification_url", s0."slack_hook_url", s0."bq_table_partition_type", s0."custom_event_message_keys", s0."log_events_updated_at", s0."notifications_every", s0."lock_schema", s0."validate_schema", s0."drop_lql_filters", s0."drop_lql_string", s0."v2_pipeline", s0."disable_tailing", s0."suggested_keys", s0."transform_copy_fields", s0."user_id", s0."notifications", s0."inserted_at", s0."updated_at" FROM "sources" AS s0 WHERE (s0."user_id" = $1) CREATE TABLE IF NOT EXISTS log_events_253abd96_ed02_48c8_bed8_1bae068350c0 (
  id TEXT PRIMARY KEY,
  body JSONB,
  event_message TEXT,
  timestamp TIMESTAMP
) INSERT INTO "tenants" ("name","suspend","external_id","jwt_secret","max_bytes_per_second","max_channels_per_client","max_concurrent_users","max_events_per_second","max_joins_per_second","private_only","inserted_at","updated_at","id") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13) -- 将一个用户的重置日期改为昨天进行测试
UPDATE "membership-true" 
SET last_free_reset_date = CURRENT_DATE - $1
WHERE email = $2 SELECT t.oid, t.typname, t.typsend, t.typreceive, t.typoutput, t.typinput,
       coalesce(d.typelem, t.typelem), coalesce(r.rngsubtype, $1), ARRAY (
  SELECT a.atttypid
  FROM pg_attribute AS a
  WHERE a.attrelid = t.typrelid AND a.attnum > $2 AND NOT a.attisdropped
  ORDER BY a.attnum
)

FROM pg_type AS t
LEFT JOIN pg_type AS d ON t.typbasetype = d.oid
LEFT JOIN pg_range AS r ON r.rngtypid = t.oid OR r.rngmultitypid = t.oid OR (t.typbasetype <> $3 AND r.rngtypid = t.typbasetype)
WHERE (t.typrelid = $4)
AND (t.typelem = $5 OR NOT EXISTS (SELECT $6 FROM pg_catalog.pg_type s WHERE s.typrelid != $7 AND s.oid = t.typelem)) -- source: dashboard
-- user: self host
-- date: 2025-08-02T11:09:47.476Z

set local search_path = '' NOTIFY supavisor_local_2_5, 'supavisor@a8d16b680918' SELECT 
    NOW() as current_time_with_timezone,
    NOW() AT TIME ZONE $1 as current_utc_time,
    EXTRACT($2 FROM NOW()) as timezone_offset_seconds SELECT name, setting FROM pg_settings WHERE name IN ($1, $2) SELECT name, setting FROM pg_settings WHERE name LIKE $1 AND name LIKE $2 