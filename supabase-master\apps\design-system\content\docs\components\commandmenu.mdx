---
title: Command Menu (cmdk)
description: A central command menu that acts as a control center with searchable actions.
component: true
---

The example below uses `cmd-j` as the shortcut instead of `cmd-k` to avoid clashing with this site's native command menu.

<ComponentPreview name="commandmenu-demo" peekCode wide />

## Usage

```tsx
import {
  CommandInput,
  CommandList,
  CommandMenu,
  CommandMenuTrigger as CommandMenuTriggerPrimitive,
  CommandProvider,
  useRegisterCommands,
} from 'ui-patterns/CommandMenu'
```

```tsx
function Commands() {
  useRegisterCommands('Action commands', [
    {
      id: 'alert',
      name: '<PERSON><PERSON>',
      action: () => alert('You triggered a command'),
    },
  ])
  useRegisterCommands('Route commands', [
    {
      id: 'supabase-website',
      name: 'Go to Supabase website',
      route: 'https://supabase.com',
    },
  ])

  return null
}

function CommandMenuTrigger() {
  return (
    <CommandMenuTriggerPrimitive>
      <Button>Open command menu</Button>
    </CommandMenuTriggerPrimitive>
  )
}

export default function CommandMenuDemo() {
  return (
    <CommandProvider openKey="j">
      <Commands />
      <CommandMenu trigger={<CommandMenuTrigger />}>
        <CommandInput />
        <CommandList />
      </CommandMenu>
    </CommandProvider>
  )
}
```

## Examples

To avoid keyboard shortcuts clashing wildly, the open shortcut is disabled for the following examples. Use the button to open the example previews.

### With experimental badge

You can add an experimental badge to any command item:

<ComponentPreview name="commandmenu-badge" />

### With icon

You can add an icon to any command item:

<ComponentPreview name="commandmenu-icon" />

### With default hidden

You can hide any command menu item unless it matches an active search query. Try searching for `open sesame` here:

<ComponentPreview name="commandmenu-hidden" />

### With force-mount

You can force-mount any command item (force it to always show, regardless of search query). You can also force-mount an entire section.

<ComponentPreview name="commandmenu-force" />

### With conditional commands

You can use the dependencies (`deps`) array on the register command options to change the registered commands in response to state changes.

Dependency equality is calculated using the `lodash` [isEqual](https://lodash.com/docs/4.17.15#isEqual) function, so you may need suitable use of `useCallback` or `useMemo` to maintain or break equality.

<ComponentPreview name="commandmenu-conditional" />

### With subpage

You can define a subpage of commands:

<ComponentPreview name="commandmenu-subpage" />

### With arbitrary subpage

You can define an arbitrary subpage that loads a custom component:

<ComponentPreview name="commandmenu-subpage-custom" />
