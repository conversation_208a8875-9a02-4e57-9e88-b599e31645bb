/packages/ui/  @supabase/design
/packages/shared-data/pricing.ts @roryw10 @kevcodez
/packages/shared-data/plans.ts @roryw10 @kevcodez
/packages/common/telemetry-constants.ts @4L3k51 @supabase/growth-eng

/apps/studio/ @supabase/Dashboard

# temporary until we've ironed out all the problems with sync workflow
/apps/docs/content/troubleshooting/ @charislam

/apps/www/ @supabase/marketing
/apps/www/public/images/blog @supabase/marketing
/apps/www/lib/redirects.js

/docker/ @supabase/dev-workflows

/apps/studio/next.config.js                                         @supabase/security
/apps/studio/components/interfaces/Billing/Payment                  @supabase/security
/apps/studio/components/interfaces/Organization/BillingSettings/    @supabase/security
/apps/studio/components/interfaces/Organization/Documents/          @supabase/security
/apps/studio/pages/new/index.tsx                                    @supabase/security
