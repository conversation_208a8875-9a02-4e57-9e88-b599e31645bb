name: Comment on external PRs

on:
  pull_request_target:
    types: [opened]

permissions:
  pull-requests: write

jobs:
  comment-on-external-pr:
    runs-on: ubuntu-latest
    if: github.event.pull_request.head.repo.full_name != github.repository
    steps:
      - name: Comment on PR
        uses: actions/github-script@v7
        with:
          script: |
            const commentBody = `
            Thanks for contributing to Supabase! ❤️ Our team will review your PR.

            A few tips for a smoother review process:
            - If you have a local version of the repo, run \`pnpm run format\` to make sure formatting checks pass.
            - Once we've reviewed your PR, please don't trivially merge master (don't click \`Update branch\` if there are no merge conflicts to be fixed). This invalidates any pre-merge checks we've run.
            `;
            github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: commentBody
            });
