/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    authors: Author;
    categories: Category;
    customers: Customer;
    events: Event;
    media: Media;
    posts: Post;
    tags: Tag;
    users: User;
    'payload-jobs': PayloadJob;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    authors: AuthorsSelect<false> | AuthorsSelect<true>;
    categories: CategoriesSelect<false> | CategoriesSelect<true>;
    customers: CustomersSelect<false> | CustomersSelect<true>;
    events: EventsSelect<false> | EventsSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    posts: PostsSelect<false> | PostsSelect<true>;
    tags: TagsSelect<false> | TagsSelect<true>;
    users: UsersSelect<false> | UsersSelect<true>;
    'payload-jobs': PayloadJobsSelect<false> | PayloadJobsSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: number;
  };
  globals: {};
  globalsSelect: {};
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: {
      schedulePublish: TaskSchedulePublish;
      inline: {
        input: unknown;
        output: unknown;
      };
    };
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "authors".
 */
export interface Author {
  id: number;
  author: string;
  author_id?: string | null;
  position?: string | null;
  author_url?: string | null;
  author_image_url?: (number | null) | Media;
  username?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: number;
  alt?: string | null;
  caption?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  prefix?: string | null;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
  sizes?: {
    thumbnail?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    square?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    small?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    medium?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    large?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    xlarge?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    og?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories".
 */
export interface Category {
  id: number;
  name: string;
  parent?: (number | null) | Category;
  breadcrumbs?:
    | {
        doc?: (number | null) | Category;
        url?: string | null;
        label?: string | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "customers".
 */
export interface Customer {
  id: number;
  name: string;
  title?: string | null;
  slug?: string | null;
  slugLock?: boolean | null;
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  description?: string | null;
  /**
   * Short description about the company
   */
  about?: string | null;
  /**
   * URL of the company website
   */
  company_url?: string | null;
  /**
   * Key statistics or metrics to highlight
   */
  stats?:
    | {
        stat: string;
        label: string;
        id?: string | null;
      }[]
    | null;
  /**
   * Miscellaneous information (e.g., Founded, Location)
   */
  misc?:
    | {
        label: string;
        text: string;
        id?: string | null;
      }[]
    | null;
  /**
   * Industry categories
   */
  industry?:
    | (
        | 'healthcare'
        | 'fintech'
        | 'ecommerce'
        | 'education'
        | 'gaming'
        | 'media'
        | 'real-estate'
        | 'saas'
        | 'social'
        | 'analytics'
        | 'ai'
        | 'developer-tools'
      )[]
    | null;
  /**
   * Size of the company
   */
  company_size?: ('startup' | 'enterprise' | 'indie_dev') | null;
  /**
   * Geographic region
   */
  region?: ('Asia' | 'Europe' | 'North America' | 'South America' | 'Africa' | 'Oceania') | null;
  /**
   * Supabase products being used
   */
  supabase_products?: ('database' | 'auth' | 'storage' | 'realtime' | 'functions' | 'vector')[] | null;
  meta?: {
    title?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (number | null) | Media;
    description?: string | null;
  };
  publishedAt?: string | null;
  logo?: (number | null) | Media;
  /**
   * Light mode logo
   */
  logo_inverse?: (number | null) | Media;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "events".
 */
export interface Event {
  id: number;
  title: string;
  slug?: string | null;
  slugLock?: boolean | null;
  /**
   * Used in the event page as subtitle.
   */
  subtitle?: string | null;
  content?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  thumb?: (number | null) | Media;
  image?: (number | null) | Media;
  /**
   * Event type
   */
  type?:
    | (
        | 'conference'
        | 'hackathon'
        | 'launch-week'
        | 'meetup'
        | 'office-hours'
        | 'talk'
        | 'webinar'
        | 'workshop'
        | 'other'
      )[]
    | null;
  date?: string | null;
  timezone?:
    | (
        | 'Africa/Abidjan'
        | 'Africa/Accra'
        | 'Africa/Addis_Ababa'
        | 'Africa/Algiers'
        | 'Africa/Asmara'
        | 'Africa/Bamako'
        | 'Africa/Bangui'
        | 'Africa/Banjul'
        | 'Africa/Bissau'
        | 'Africa/Blantyre'
        | 'Africa/Brazzaville'
        | 'Africa/Bujumbura'
        | 'Africa/Cairo'
        | 'Africa/Casablanca'
        | 'Africa/Ceuta'
        | 'Africa/Conakry'
        | 'Africa/Dakar'
        | 'Africa/Dar_es_Salaam'
        | 'Africa/Djibouti'
        | 'Africa/Douala'
        | 'Africa/El_Aaiun'
        | 'Africa/Freetown'
        | 'Africa/Gaborone'
        | 'Africa/Harare'
        | 'Africa/Johannesburg'
        | 'Africa/Juba'
        | 'Africa/Kampala'
        | 'Africa/Khartoum'
        | 'Africa/Kigali'
        | 'Africa/Kinshasa'
        | 'Africa/Lagos'
        | 'Africa/Libreville'
        | 'Africa/Lome'
        | 'Africa/Luanda'
        | 'Africa/Lubumbashi'
        | 'Africa/Lusaka'
        | 'Africa/Malabo'
        | 'Africa/Maputo'
        | 'Africa/Maseru'
        | 'Africa/Mbabane'
        | 'Africa/Mogadishu'
        | 'Africa/Monrovia'
        | 'Africa/Nairobi'
        | 'Africa/Ndjamena'
        | 'Africa/Niamey'
        | 'Africa/Nouakchott'
        | 'Africa/Ouagadougou'
        | 'Africa/Porto-Novo'
        | 'Africa/Sao_Tome'
        | 'Africa/Tripoli'
        | 'Africa/Tunis'
        | 'Africa/Windhoek'
        | 'America/Adak'
        | 'America/Anchorage'
        | 'America/Anguilla'
        | 'America/Antigua'
        | 'America/Araguaina'
        | 'America/Argentina/Buenos_Aires'
        | 'America/Argentina/Catamarca'
        | 'America/Argentina/Cordoba'
        | 'America/Argentina/Jujuy'
        | 'America/Argentina/La_Rioja'
        | 'America/Argentina/Mendoza'
        | 'America/Argentina/Rio_Gallegos'
        | 'America/Argentina/Salta'
        | 'America/Argentina/San_Juan'
        | 'America/Argentina/San_Luis'
        | 'America/Argentina/Tucuman'
        | 'America/Argentina/Ushuaia'
        | 'America/Aruba'
        | 'America/Asuncion'
        | 'America/Atikokan'
        | 'America/Bahia'
        | 'America/Bahia_Banderas'
        | 'America/Barbados'
        | 'America/Belem'
        | 'America/Belize'
        | 'America/Blanc-Sablon'
        | 'America/Boa_Vista'
        | 'America/Bogota'
        | 'America/Boise'
        | 'America/Cambridge_Bay'
        | 'America/Campo_Grande'
        | 'America/Cancun'
        | 'America/Caracas'
        | 'America/Cayenne'
        | 'America/Cayman'
        | 'America/Chicago'
        | 'America/Chihuahua'
        | 'America/Costa_Rica'
        | 'America/Creston'
        | 'America/Cuiaba'
        | 'America/Curacao'
        | 'America/Danmarkshavn'
        | 'America/Dawson'
        | 'America/Dawson_Creek'
        | 'America/Denver'
        | 'America/Detroit'
        | 'America/Dominica'
        | 'America/Edmonton'
        | 'America/Eirunepe'
        | 'America/El_Salvador'
        | 'America/Fort_Nelson'
        | 'America/Fortaleza'
        | 'America/Glace_Bay'
        | 'America/Godthab'
        | 'America/Goose_Bay'
        | 'America/Grand_Turk'
        | 'America/Grenada'
        | 'America/Guadeloupe'
        | 'America/Guatemala'
        | 'America/Guayaquil'
        | 'America/Guyana'
        | 'America/Halifax'
        | 'America/Havana'
        | 'America/Hermosillo'
        | 'America/Indiana/Indianapolis'
        | 'America/Indiana/Knox'
        | 'America/Indiana/Marengo'
        | 'America/Indiana/Petersburg'
        | 'America/Indiana/Tell_City'
        | 'America/Indiana/Vevay'
        | 'America/Indiana/Vincennes'
        | 'America/Indiana/Winamac'
        | 'America/Inuvik'
        | 'America/Iqaluit'
        | 'America/Jamaica'
        | 'America/Juneau'
        | 'America/Kentucky/Louisville'
        | 'America/Kentucky/Monticello'
        | 'America/Kralendijk'
        | 'America/La_Paz'
        | 'America/Lima'
        | 'America/Los_Angeles'
        | 'America/Lower_Princes'
        | 'America/Maceio'
        | 'America/Managua'
        | 'America/Manaus'
        | 'America/Marigot'
        | 'America/Martinique'
        | 'America/Matamoros'
        | 'America/Mazatlan'
        | 'America/Menominee'
        | 'America/Merida'
        | 'America/Metlakatla'
        | 'America/Mexico_City'
        | 'America/Miquelon'
        | 'America/Moncton'
        | 'America/Monterrey'
        | 'America/Montevideo'
        | 'America/Montserrat'
        | 'America/Nassau'
        | 'America/New_York'
        | 'America/Nipigon'
        | 'America/Nome'
        | 'America/Noronha'
        | 'America/North_Dakota/Beulah'
        | 'America/North_Dakota/Center'
        | 'America/North_Dakota/New_Salem'
        | 'America/Ojinaga'
        | 'America/Panama'
        | 'America/Pangnirtung'
        | 'America/Paramaribo'
        | 'America/Phoenix'
        | 'America/Port-au-Prince'
        | 'America/Port_of_Spain'
        | 'America/Porto_Velho'
        | 'America/Puerto_Rico'
        | 'America/Punta_Arenas'
        | 'America/Rainy_River'
        | 'America/Rankin_Inlet'
        | 'America/Recife'
        | 'America/Regina'
        | 'America/Resolute'
        | 'America/Rio_Branco'
        | 'America/Santarem'
        | 'America/Santiago'
        | 'America/Santo_Domingo'
        | 'America/Sao_Paulo'
        | 'America/Scoresbysund'
        | 'America/Sitka'
        | 'America/St_Barthelemy'
        | 'America/St_Johns'
        | 'America/St_Kitts'
        | 'America/St_Lucia'
        | 'America/St_Thomas'
        | 'America/St_Vincent'
        | 'America/Swift_Current'
        | 'America/Tegucigalpa'
        | 'America/Thule'
        | 'America/Thunder_Bay'
        | 'America/Tijuana'
        | 'America/Toronto'
        | 'America/Tortola'
        | 'America/Vancouver'
        | 'America/Whitehorse'
        | 'America/Winnipeg'
        | 'America/Yakutat'
        | 'America/Yellowknife'
        | 'Antarctica/Casey'
        | 'Antarctica/Davis'
        | 'Antarctica/DumontDUrville'
        | 'Antarctica/Macquarie'
        | 'Antarctica/Mawson'
        | 'Antarctica/McMurdo'
        | 'Antarctica/Palmer'
        | 'Antarctica/Rothera'
        | 'Antarctica/Syowa'
        | 'Antarctica/Troll'
        | 'Antarctica/Vostok'
        | 'Arctic/Longyearbyen'
        | 'Asia/Aden'
        | 'Asia/Almaty'
        | 'Asia/Amman'
        | 'Asia/Anadyr'
        | 'Asia/Aqtau'
        | 'Asia/Aqtobe'
        | 'Asia/Ashgabat'
        | 'Asia/Atyrau'
        | 'Asia/Baghdad'
        | 'Asia/Bahrain'
        | 'Asia/Baku'
        | 'Asia/Bangkok'
        | 'Asia/Barnaul'
        | 'Asia/Beirut'
        | 'Asia/Bishkek'
        | 'Asia/Brunei'
        | 'Asia/Chita'
        | 'Asia/Choibalsan'
        | 'Asia/Colombo'
        | 'Asia/Damascus'
        | 'Asia/Dhaka'
        | 'Asia/Dili'
        | 'Asia/Dubai'
        | 'Asia/Dushanbe'
        | 'Asia/Famagusta'
        | 'Asia/Gaza'
        | 'Asia/Hebron'
        | 'Asia/Ho_Chi_Minh'
        | 'Asia/Hong_Kong'
        | 'Asia/Hovd'
        | 'Asia/Irkutsk'
        | 'Asia/Istanbul'
        | 'Asia/Jakarta'
        | 'Asia/Jayapura'
        | 'Asia/Jerusalem'
        | 'Asia/Kabul'
        | 'Asia/Kamchatka'
        | 'Asia/Karachi'
        | 'Asia/Kathmandu'
        | 'Asia/Khandyga'
        | 'Asia/Kolkata'
        | 'Asia/Krasnoyarsk'
        | 'Asia/Kuala_Lumpur'
        | 'Asia/Kuching'
        | 'Asia/Kuwait'
        | 'Asia/Macau'
        | 'Asia/Magadan'
        | 'Asia/Makassar'
        | 'Asia/Manila'
        | 'Asia/Muscat'
        | 'Asia/Nicosia'
        | 'Asia/Novokuznetsk'
        | 'Asia/Novosibirsk'
        | 'Asia/Omsk'
        | 'Asia/Oral'
        | 'Asia/Phnom_Penh'
        | 'Asia/Pontianak'
        | 'Asia/Pyongyang'
        | 'Asia/Qatar'
        | 'Asia/Qostanay'
        | 'Asia/Qyzylorda'
        | 'Asia/Riyadh'
        | 'Asia/Sakhalin'
        | 'Asia/Samarkand'
        | 'Asia/Seoul'
        | 'Asia/Shanghai'
        | 'Asia/Singapore'
        | 'Asia/Srednekolymsk'
        | 'Asia/Taipei'
        | 'Asia/Tashkent'
        | 'Asia/Tbilisi'
        | 'Asia/Tehran'
        | 'Asia/Thimphu'
        | 'Asia/Tokyo'
        | 'Asia/Tomsk'
        | 'Asia/Ulaanbaatar'
        | 'Asia/Urumqi'
        | 'Asia/Ust-Nera'
        | 'Asia/Vientiane'
        | 'Asia/Vladivostok'
        | 'Asia/Yakutsk'
        | 'Asia/Yangon'
        | 'Asia/Yekaterinburg'
        | 'Asia/Yerevan'
        | 'Atlantic/Azores'
        | 'Atlantic/Bermuda'
        | 'Atlantic/Canary'
        | 'Atlantic/Cape_Verde'
        | 'Atlantic/Faroe'
        | 'Atlantic/Madeira'
        | 'Atlantic/Reykjavik'
        | 'Atlantic/South_Georgia'
        | 'Atlantic/St_Helena'
        | 'Atlantic/Stanley'
        | 'Australia/Adelaide'
        | 'Australia/Brisbane'
        | 'Australia/Broken_Hill'
        | 'Australia/Currie'
        | 'Australia/Darwin'
        | 'Australia/Eucla'
        | 'Australia/Hobart'
        | 'Australia/Lindeman'
        | 'Australia/Lord_Howe'
        | 'Australia/Melbourne'
        | 'Australia/Perth'
        | 'Australia/Sydney'
        | 'Europe/Amsterdam'
        | 'Europe/Andorra'
        | 'Europe/Astrakhan'
        | 'Europe/Athens'
        | 'Europe/Belgrade'
        | 'Europe/Berlin'
        | 'Europe/Bratislava'
        | 'Europe/Brussels'
        | 'Europe/Bucharest'
        | 'Europe/Budapest'
        | 'Europe/Busingen'
        | 'Europe/Chisinau'
        | 'Europe/Copenhagen'
        | 'Europe/Dublin'
        | 'Europe/Gibraltar'
        | 'Europe/Guernsey'
        | 'Europe/Helsinki'
        | 'Europe/Isle_of_Man'
        | 'Europe/Istanbul'
        | 'Europe/Jersey'
        | 'Europe/Kaliningrad'
        | 'Europe/Kiev'
        | 'Europe/Kirov'
        | 'Europe/Lisbon'
        | 'Europe/Ljubljana'
        | 'Europe/London'
        | 'Europe/Luxembourg'
        | 'Europe/Madrid'
        | 'Europe/Malta'
        | 'Europe/Mariehamn'
        | 'Europe/Minsk'
        | 'Europe/Monaco'
        | 'Europe/Moscow'
        | 'Europe/Oslo'
        | 'Europe/Paris'
        | 'Europe/Podgorica'
        | 'Europe/Prague'
        | 'Europe/Riga'
        | 'Europe/Rome'
        | 'Europe/Samara'
        | 'Europe/San_Marino'
        | 'Europe/Sarajevo'
        | 'Europe/Saratov'
        | 'Europe/Simferopol'
        | 'Europe/Skopje'
        | 'Europe/Sofia'
        | 'Europe/Stockholm'
        | 'Europe/Tallinn'
        | 'Europe/Tirane'
        | 'Europe/Ulyanovsk'
        | 'Europe/Uzhgorod'
        | 'Europe/Vaduz'
        | 'Europe/Vatican'
        | 'Europe/Vienna'
        | 'Europe/Vilnius'
        | 'Europe/Volgograd'
        | 'Europe/Warsaw'
        | 'Europe/Zagreb'
        | 'Europe/Zaporozhye'
        | 'Europe/Zurich'
        | 'Indian/Antananarivo'
        | 'Indian/Chagos'
        | 'Indian/Christmas'
        | 'Indian/Cocos'
        | 'Indian/Comoro'
        | 'Indian/Kerguelen'
        | 'Indian/Mahe'
        | 'Indian/Maldives'
        | 'Indian/Mauritius'
        | 'Indian/Mayotte'
        | 'Indian/Reunion'
        | 'Pacific/Apia'
        | 'Pacific/Auckland'
        | 'Pacific/Bougainville'
        | 'Pacific/Chatham'
        | 'Pacific/Chuuk'
        | 'Pacific/Easter'
        | 'Pacific/Efate'
        | 'Pacific/Enderbury'
        | 'Pacific/Fakaofo'
        | 'Pacific/Fiji'
        | 'Pacific/Funafuti'
        | 'Pacific/Galapagos'
        | 'Pacific/Gambier'
        | 'Pacific/Guadalcanal'
        | 'Pacific/Guam'
        | 'Pacific/Honolulu'
        | 'Pacific/Kiritimati'
        | 'Pacific/Kosrae'
        | 'Pacific/Kwajalein'
        | 'Pacific/Majuro'
        | 'Pacific/Marquesas'
        | 'Pacific/Midway'
        | 'Pacific/Nauru'
        | 'Pacific/Niue'
        | 'Pacific/Norfolk'
        | 'Pacific/Noumea'
        | 'Pacific/Pago_Pago'
        | 'Pacific/Palau'
        | 'Pacific/Pitcairn'
        | 'Pacific/Pohnpei'
        | 'Pacific/Port_Moresby'
        | 'Pacific/Rarotonga'
        | 'Pacific/Saipan'
        | 'Pacific/Tahiti'
        | 'Pacific/Tarawa'
        | 'Pacific/Tongatapu'
        | 'Pacific/Wake'
        | 'Pacific/Wallis'
        | 'UTC'
      )
    | null;
  showEndDate?: boolean | null;
  /**
   * If "showEndDate" is true, this will define when the event terminates.
   */
  endDate?: string | null;
  /**
   * Text string to display on the event page to indicate the duration of the event. (e.g. "45 mins", "2 days")
   */
  duration?: string | null;
  /**
   * Events that are will remain available on the events page after the event has ended.
   */
  onDemand?: boolean | null;
  /**
   * When true, the event page will not be built. It will link directly to an external event page (requires Link to be set)
   */
  disablePageBuild?: boolean | null;
  /**
   * Used on event previews to link to a custom page if "disablePageBuild" is true.
   */
  link?: {
    href?: string | null;
    target?: ('_self' | '_blank') | null;
  };
  /**
   * Main CTA button on the event page
   */
  mainCta?: {
    href?: string | null;
    target?: ('_self' | '_blank') | null;
    label?: string | null;
    disabled?: boolean | null;
    /**
     * Text for the main CTA button if "mainCta.disabled" is true.
     */
    disabled_label?: string | null;
  };
  company?: {
    /**
     * If an external company is collaborating with the event, this will display their logo on the event page.
     */
    showCompany?: boolean | null;
    name?: string | null;
    websiteUrl?: string | null;
    logo?: (number | null) | Media;
    /**
     * Light mode logo
     */
    logo_light?: (number | null) | Media;
  };
  participants?: {
    /**
     * Could be speakers, authors, guests, etc. It would source from Authors collections.
     */
    showParticipants?: boolean | null;
    participants?: (number | Author)[] | null;
  };
  meta?: {
    title?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (number | null) | Media;
    description?: string | null;
  };
  publishedAt?: string | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "posts".
 */
export interface Post {
  id: number;
  title: string;
  slug?: string | null;
  slugLock?: boolean | null;
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  thumb?: (number | null) | Media;
  image?: (number | null) | Media;
  categories?: (number | Category)[] | null;
  /**
   * Select a launch week to show launch week summary at the bottom of the blog post.
   */
  launchweek?: ('6' | '7' | '8' | 'x' | 'ga' | '12' | '13' | '14' | '15') | null;
  readingTime?: number | null;
  date?: string | null;
  toc_depth?: number | null;
  description?: string | null;
  authors?: (number | Author)[] | null;
  tags?: (number | Tag)[] | null;
  meta?: {
    title?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (number | null) | Media;
    description?: string | null;
  };
  publishedAt?: string | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tags".
 */
export interface Tag {
  id: number;
  name: string;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: number;
  roles?: ('admin' | 'editor')[] | null;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs".
 */
export interface PayloadJob {
  id: number;
  /**
   * Input data provided to the job
   */
  input?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  taskStatus?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  completedAt?: string | null;
  totalTried?: number | null;
  /**
   * If hasError is true this job will not be retried
   */
  hasError?: boolean | null;
  /**
   * If hasError is true, this is the error that caused it
   */
  error?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * Task execution log
   */
  log?:
    | {
        executedAt: string;
        completedAt: string;
        taskSlug: 'inline' | 'schedulePublish';
        taskID: string;
        input?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        output?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        state: 'failed' | 'succeeded';
        error?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        id?: string | null;
      }[]
    | null;
  taskSlug?: ('inline' | 'schedulePublish') | null;
  queue?: string | null;
  waitUntil?: string | null;
  processing?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: number;
  document?:
    | ({
        relationTo: 'authors';
        value: number | Author;
      } | null)
    | ({
        relationTo: 'categories';
        value: number | Category;
      } | null)
    | ({
        relationTo: 'customers';
        value: number | Customer;
      } | null)
    | ({
        relationTo: 'events';
        value: number | Event;
      } | null)
    | ({
        relationTo: 'media';
        value: number | Media;
      } | null)
    | ({
        relationTo: 'posts';
        value: number | Post;
      } | null)
    | ({
        relationTo: 'tags';
        value: number | Tag;
      } | null)
    | ({
        relationTo: 'users';
        value: number | User;
      } | null)
    | ({
        relationTo: 'payload-jobs';
        value: number | PayloadJob;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: number;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: number;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "authors_select".
 */
export interface AuthorsSelect<T extends boolean = true> {
  author?: T;
  author_id?: T;
  position?: T;
  author_url?: T;
  author_image_url?: T;
  username?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories_select".
 */
export interface CategoriesSelect<T extends boolean = true> {
  name?: T;
  parent?: T;
  breadcrumbs?:
    | T
    | {
        doc?: T;
        url?: T;
        label?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "customers_select".
 */
export interface CustomersSelect<T extends boolean = true> {
  name?: T;
  title?: T;
  slug?: T;
  slugLock?: T;
  content?: T;
  description?: T;
  about?: T;
  company_url?: T;
  stats?:
    | T
    | {
        stat?: T;
        label?: T;
        id?: T;
      };
  misc?:
    | T
    | {
        label?: T;
        text?: T;
        id?: T;
      };
  industry?: T;
  company_size?: T;
  region?: T;
  supabase_products?: T;
  meta?:
    | T
    | {
        title?: T;
        image?: T;
        description?: T;
      };
  publishedAt?: T;
  logo?: T;
  logo_inverse?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "events_select".
 */
export interface EventsSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  slugLock?: T;
  subtitle?: T;
  content?: T;
  thumb?: T;
  image?: T;
  type?: T;
  date?: T;
  timezone?: T;
  showEndDate?: T;
  endDate?: T;
  duration?: T;
  onDemand?: T;
  disablePageBuild?: T;
  link?:
    | T
    | {
        href?: T;
        target?: T;
      };
  mainCta?:
    | T
    | {
        href?: T;
        target?: T;
        label?: T;
        disabled?: T;
        disabled_label?: T;
      };
  company?:
    | T
    | {
        showCompany?: T;
        name?: T;
        websiteUrl?: T;
        logo?: T;
        logo_light?: T;
      };
  participants?:
    | T
    | {
        showParticipants?: T;
        participants?: T;
      };
  meta?:
    | T
    | {
        title?: T;
        image?: T;
        description?: T;
      };
  publishedAt?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  caption?: T;
  prefix?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
  sizes?:
    | T
    | {
        thumbnail?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        square?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        small?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        medium?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        large?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        xlarge?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        og?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "posts_select".
 */
export interface PostsSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  slugLock?: T;
  content?: T;
  thumb?: T;
  image?: T;
  categories?: T;
  launchweek?: T;
  readingTime?: T;
  date?: T;
  toc_depth?: T;
  description?: T;
  authors?: T;
  tags?: T;
  meta?:
    | T
    | {
        title?: T;
        image?: T;
        description?: T;
      };
  publishedAt?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tags_select".
 */
export interface TagsSelect<T extends boolean = true> {
  name?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  roles?: T;
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs_select".
 */
export interface PayloadJobsSelect<T extends boolean = true> {
  input?: T;
  taskStatus?: T;
  completedAt?: T;
  totalTried?: T;
  hasError?: T;
  error?: T;
  log?:
    | T
    | {
        executedAt?: T;
        completedAt?: T;
        taskSlug?: T;
        taskID?: T;
        input?: T;
        output?: T;
        state?: T;
        error?: T;
        id?: T;
      };
  taskSlug?: T;
  queue?: T;
  waitUntil?: T;
  processing?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskSchedulePublish".
 */
export interface TaskSchedulePublish {
  input: {
    type?: ('publish' | 'unpublish') | null;
    locale?: string | null;
    doc?:
      | ({
          relationTo: 'customers';
          value: number | Customer;
        } | null)
      | ({
          relationTo: 'events';
          value: number | Event;
        } | null)
      | ({
          relationTo: 'posts';
          value: number | Post;
        } | null);
    global?: string | null;
    user?: (number | null) | User;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "BannerBlock".
 */
export interface BannerBlock {
  style: 'info' | 'warning' | 'error' | 'success';
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  id?: string | null;
  blockName?: string | null;
  blockType: 'banner';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CodeBlock".
 */
export interface CodeBlock {
  language?: ('typescript' | 'javascript' | 'css') | null;
  code: string;
  id?: string | null;
  blockName?: string | null;
  blockType: 'code';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "MediaBlock".
 */
export interface MediaBlock {
  media: number | Media;
  id?: string | null;
  blockName?: string | null;
  blockType: 'mediaBlock';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "QuoteBlock".
 */
export interface QuoteBlock {
  img?: (number | null) | Media;
  caption?: string | null;
  text: string;
  id?: string | null;
  blockName?: string | null;
  blockType: 'quote';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "YouTubeBlock".
 */
export interface YouTubeBlock {
  /**
   * Enter the YouTube video ID (e.g., "dQw4w9WgXcQ" from https://www.youtube.com/watch?v=dQw4w9WgXcQ)
   */
  youtubeId: string;
  id?: string | null;
  blockName?: string | null;
  blockType: 'youtube';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}