---
title: Introduction
description: Components and patterns that you can copy and paste into Supabase apps. Accessible. Customizable. Open Source.
---

## FAQ

<Accordion type="multiple">

<AccordionItem value="faq-1">
	<AccordionTrigger>
		Why copy/paste and not packaged as a dependency?
	</AccordionTrigger>
	<AccordionContent>
The idea behind this is to give you ownership and control over the code, allowing you to decide how the components are built and styled.

Start with some sensible defaults, then customize the components to your needs.

One of the drawback of packaging the components in an npm package is that the style is coupled with the implementation. _The design of your components should be separate from their implementation._

</AccordionContent>
</AccordionItem>

</Accordion>
