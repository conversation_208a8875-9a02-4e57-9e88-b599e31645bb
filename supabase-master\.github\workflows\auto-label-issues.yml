name: Auto Label Issues
on:
  issues:
    types: [opened, reopened]
jobs:
  check-external:
    runs-on: ubuntu-latest
    permissions:
      issues: write
    steps:
    - name: Check if organization member
      id: is-org-member
      uses: <PERSON><PERSON><PERSON><PERSON>/is-organization-member@1.0.0
      with:
        organization: ${{ github.repository_owner }}
        username: ${{ github.event.issue.user.login }}
        token: ${{ secrets.GITHUB_TOKEN }}
    - name: label-member
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        GH_REPO: ${{ github.repository }}
        NUMBER: ${{ github.event.issue.number }}
      run: |
        if [ ${{ steps.is-org-member.outputs.result }} != "true" ]; then
          echo "User is outside of organization, labeling external"
          gh issue edit "$NUMBER" --add-label "external-issue"
        else
          echo "User is within the organization, labeling internal"
          gh issue edit "$NUMBER" --add-label "internal-issue"
        fi
  triage-new:
    runs-on: ubuntu-latest
    permissions:
      issues: write
    steps:
      - name: Label triage
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GH_REPO: ${{ github.repository }}
          NUMBER: ${{ github.event.issue.number }}
          LABELS: to-triage
        run: |
          echo "Applying triage label for new issue"
          gh issue edit "$NUMBER" --add-label "$LABELS"
  spam-detection:
    runs-on: ubuntu-latest
    permissions:
      issues: write
    steps:
      - name: Check GitHub Issue for spam
        env:
          POST_URL: ${{ secrets.POST_URL }}
          BEARER_TOKEN: ${{ secrets.BEARER_TOKEN }}
          NUMBER: ${{ github.event.issue.number }}
        run: |
          RESPONSE=$(curl -s -X POST "$POST_URL" \
            -H "Authorization: Bearer $BEARER_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{"issue_id": $NUMBER}')
          echo "spam_response=$RESPONSE" >> $GITHUB_OUTPUT
      - name: Use spam detector output to label issue
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GH_REPO: ${{ github.repository }}
          NUMBER: ${{ github.event.issue.number }}
          LABEL: flagged
        run: |
          IS_SPAM=$(echo "$SPAM_RESPONSE" | jq -r '.spam')
          if [ "$IS_SPAM" == "true" ]; then
            echo "Applying flagged label for new issue suspected of spam"
            gh issue edit "$NUMBER" --add-label "$LABEL"
          fi
