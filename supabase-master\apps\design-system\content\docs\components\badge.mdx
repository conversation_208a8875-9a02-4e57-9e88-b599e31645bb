---
title: Badge
description: Displays a badge or a component that looks like a badge.
component: true
source:
  shadcn: true
---

<ComponentPreview name="badge-demo" peekCode wide />

## Installation

<Tabs defaultValue="cli">

<TabsList>
  <TabsTrigger value="cli">CLI</TabsTrigger>
  <TabsTrigger value="manual">Manual</TabsTrigger>
</TabsList>
<TabsContent value="cli">

```bash
npx shadcn-ui@latest add badge
```

</TabsContent>

<TabsContent value="manual">

<Steps>

<Step>Copy and paste the following code into your project.</Step>

<ComponentSource name="badge" />

<Step>Update the import paths to match your project setup.</Step>

</Steps>

</TabsContent>

</Tabs>

## Usage

```tsx
import { Badge } from '@/components/ui/badge'
```

```tsx
<Badge variant="outline">Badge</Badge>
```

### Link

You can use the `badgeVariants` helper to create a link that looks like a badge.

```tsx
import { badgeVariants } from '@/components/ui/badge'
```

```tsx
<Link className={badgeVariants({ variant: 'outline' })}>Badge</Link>
```

## Examples

### Default

<ComponentPreview name="badge-demo" peekCode wide />

---

### Secondary

<ComponentPreview name="badge-secondary" />

---

### Outline

<ComponentPreview name="badge-outline" />

---

### Destructive

<ComponentPreview name="badge-destructive" />
