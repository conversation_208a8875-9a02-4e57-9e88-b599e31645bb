name: Self-Host Tests Smoke

on:
  pull_request:
    branches: ['master']
    paths:
      - 'docker/**/*'

# Cancel old builds on new commit for same workflow + branch/PR
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          sparse-checkout: |
            docker/
      - name: Run docker-compose up
        # Ensure all services can be started and healthy with default config
        run: cd docker && cp .env.example .env && docker compose up --wait
