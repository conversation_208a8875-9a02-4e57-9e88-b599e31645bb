---
description: Docs Testing Procedure
globs: apps/docs/**/*.test.ts
alwaysApply: false
---

# Docs Test Requirements

Rules for running tests in the docs application, ensuring proper Supabase setup and test execution.

<rule>
name: docs_test_requirements
description: Standards for running tests in the docs application with proper Supabase setup
filters:
  # Match test files in the docs app
  - type: file_extension
    pattern: "\\.(test|spec)\\.(ts|tsx)$"
  - type: path
    pattern: "^apps/docs/.*"
  # Match test execution events
  - type: event
    pattern: "test_execution"

actions:
  - type: suggest
    message: |
      Before running tests in the docs app:

      1. Check Supabase status:
         ```bash
         pnpm supabase status
         ```

      2. If Supabase is not running:
         ```bash
         pnpm supabase start
         ```

      3. Reset the database to ensure clean state:
         ```bash
         pnpm supabase db reset --local
         ```

      4. Run the tests:
         ```bash
         pnpm run -F docs test:local:unwatch
         ```

      Important notes:
      - Always ensure Supabase is running before tests
      - Database must be reset to ensure clean state
      - Use test:local:unwatch to run tests without watch mode
      - Tests are located in apps/docs/**/*.{test,spec}.{ts,tsx}

examples:
  - input: |
      # Bad: Running tests without proper setup
      pnpm run -F docs test
      pnpm run -F docs test:local

      # Good: Proper test execution sequence
      pnpm supabase status
      pnpm supabase start  # if not running
      pnpm supabase db reset --local
      pnpm run -F docs test:local:unwatch
    output: "Correctly executed docs tests with proper Supabase setup"

metadata:
  priority: high
  version: 1.0
</rule>
