name: Check Code with Prettier

on:
  pull_request:
    branches:
      - 'master'

# Cancel old builds on new commit for same workflow + branch/PR
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  format:
    runs-on: ubuntu-latest
    steps:
      - name: Check out repo
        uses: actions/checkout@v4
        with:
          sparse-checkout: apps
      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'pnpm'
      - name: Download dependencies
        run: |
          pnpm i
      - name: Run prettier
        run: |-
          pnpm run test:prettier

  # i18n is not a node package, so we handle that one separately
  format-i18n:
    runs-on: ubuntu-latest
    steps:
      - name: Check out repo
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            i18n
      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'pnpm'
      - name: Download dependencies
        run: |
          pnpm i
      - name: Run prettier
        run: |-
          pnpm exec prettier -c 'i18n/**/*.{js,jsx,ts,tsx,css,md,mdx,json}'

  format-sql:
    runs-on: ubuntu-latest
    steps:
      - name: Check out repo
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            apps/docs/pages
            apps/docs/content
      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'pnpm'
      - name: Download dependencies
        run: |
          pnpm i
      - name: Run prettier
        run: |-
          # Check mdx files which contain sql code blocks
          grep -lr '```sql' apps/docs/{pages,content}/**/*.mdx | xargs pnpm exec prettier -c
