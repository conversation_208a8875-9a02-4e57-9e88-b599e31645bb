{"id": "b20fdabe-061b-43a4-8abf-b401ef5818d1", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"cms-payload.authors": {"name": "authors", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "author": {"name": "author", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "author_id": {"name": "author_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "position": {"name": "position", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "author_url": {"name": "author_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "author_image_url_id": {"name": "author_image_url_id", "type": "integer", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"authors_author_image_url_idx": {"name": "authors_author_image_url_idx", "columns": [{"expression": "author_image_url_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "authors_updated_at_idx": {"name": "authors_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "authors_created_at_idx": {"name": "authors_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"authors_author_image_url_id_media_id_fk": {"name": "authors_author_image_url_id_media_id_fk", "tableFrom": "authors", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["author_image_url_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.categories_breadcrumbs": {"name": "categories_breadcrumbs", "schema": "cms-payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "doc_id": {"name": "doc_id", "type": "integer", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"categories_breadcrumbs_order_idx": {"name": "categories_breadcrumbs_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "categories_breadcrumbs_parent_id_idx": {"name": "categories_breadcrumbs_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "categories_breadcrumbs_doc_idx": {"name": "categories_breadcrumbs_doc_idx", "columns": [{"expression": "doc_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"categories_breadcrumbs_doc_id_categories_id_fk": {"name": "categories_breadcrumbs_doc_id_categories_id_fk", "tableFrom": "categories_breadcrumbs", "tableTo": "categories", "schemaTo": "cms-payload", "columnsFrom": ["doc_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "categories_breadcrumbs_parent_id_fk": {"name": "categories_breadcrumbs_parent_id_fk", "tableFrom": "categories_breadcrumbs", "tableTo": "categories", "schemaTo": "cms-payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.categories": {"name": "categories", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"categories_parent_idx": {"name": "categories_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "categories_updated_at_idx": {"name": "categories_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "categories_created_at_idx": {"name": "categories_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"categories_parent_id_categories_id_fk": {"name": "categories_parent_id_categories_id_fk", "tableFrom": "categories", "tableTo": "categories", "schemaTo": "cms-payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.customers_stats": {"name": "customers_stats", "schema": "cms-payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "stat": {"name": "stat", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"customers_stats_order_idx": {"name": "customers_stats_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customers_stats_parent_id_idx": {"name": "customers_stats_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"customers_stats_parent_id_fk": {"name": "customers_stats_parent_id_fk", "tableFrom": "customers_stats", "tableTo": "customers", "schemaTo": "cms-payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.customers_misc": {"name": "customers_misc", "schema": "cms-payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "text": {"name": "text", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"customers_misc_order_idx": {"name": "customers_misc_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customers_misc_parent_id_idx": {"name": "customers_misc_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"customers_misc_parent_id_fk": {"name": "customers_misc_parent_id_fk", "tableFrom": "customers_misc", "tableTo": "customers", "schemaTo": "cms-payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.customers_industry": {"name": "customers_industry", "schema": "cms-payload", "columns": {"order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "enum_customers_industry", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}}, "indexes": {"customers_industry_order_idx": {"name": "customers_industry_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customers_industry_parent_idx": {"name": "customers_industry_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"customers_industry_parent_fk": {"name": "customers_industry_parent_fk", "tableFrom": "customers_industry", "tableTo": "customers", "schemaTo": "cms-payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.customers_supabase_products": {"name": "customers_supabase_products", "schema": "cms-payload", "columns": {"order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "enum_customers_supabase_products", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}}, "indexes": {"customers_supabase_products_order_idx": {"name": "customers_supabase_products_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customers_supabase_products_parent_idx": {"name": "customers_supabase_products_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"customers_supabase_products_parent_fk": {"name": "customers_supabase_products_parent_fk", "tableFrom": "customers_supabase_products", "tableTo": "customers", "schemaTo": "cms-payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.customers": {"name": "customers", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug_lock": {"name": "slug_lock", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "about": {"name": "about", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "company_url": {"name": "company_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "company_size": {"name": "company_size", "type": "enum_customers_company_size", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false}, "region": {"name": "region", "type": "enum_customers_region", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false}, "meta_title": {"name": "meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_image_id": {"name": "meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_description": {"name": "meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "logo_id": {"name": "logo_id", "type": "integer", "primaryKey": false, "notNull": false}, "logo_inverse_id": {"name": "logo_inverse_id", "type": "integer", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "_status": {"name": "_status", "type": "enum_customers_status", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false, "default": "'draft'"}}, "indexes": {"customers_slug_idx": {"name": "customers_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customers_meta_meta_image_idx": {"name": "customers_meta_meta_image_idx", "columns": [{"expression": "meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customers_logo_idx": {"name": "customers_logo_idx", "columns": [{"expression": "logo_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customers_logo_inverse_idx": {"name": "customers_logo_inverse_idx", "columns": [{"expression": "logo_inverse_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customers_updated_at_idx": {"name": "customers_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customers_created_at_idx": {"name": "customers_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customers__status_idx": {"name": "customers__status_idx", "columns": [{"expression": "_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"customers_meta_image_id_media_id_fk": {"name": "customers_meta_image_id_media_id_fk", "tableFrom": "customers", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "customers_logo_id_media_id_fk": {"name": "customers_logo_id_media_id_fk", "tableFrom": "customers", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["logo_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "customers_logo_inverse_id_media_id_fk": {"name": "customers_logo_inverse_id_media_id_fk", "tableFrom": "customers", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["logo_inverse_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload._customers_v_version_stats": {"name": "_customers_v_version_stats", "schema": "cms-payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "stat": {"name": "stat", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_customers_v_version_stats_order_idx": {"name": "_customers_v_version_stats_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_customers_v_version_stats_parent_id_idx": {"name": "_customers_v_version_stats_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_customers_v_version_stats_parent_id_fk": {"name": "_customers_v_version_stats_parent_id_fk", "tableFrom": "_customers_v_version_stats", "tableTo": "_customers_v", "schemaTo": "cms-payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload._customers_v_version_misc": {"name": "_customers_v_version_misc", "schema": "cms-payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "text": {"name": "text", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_customers_v_version_misc_order_idx": {"name": "_customers_v_version_misc_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_customers_v_version_misc_parent_id_idx": {"name": "_customers_v_version_misc_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_customers_v_version_misc_parent_id_fk": {"name": "_customers_v_version_misc_parent_id_fk", "tableFrom": "_customers_v_version_misc", "tableTo": "_customers_v", "schemaTo": "cms-payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload._customers_v_version_industry": {"name": "_customers_v_version_industry", "schema": "cms-payload", "columns": {"order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "enum__customers_v_version_industry", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}}, "indexes": {"_customers_v_version_industry_order_idx": {"name": "_customers_v_version_industry_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_customers_v_version_industry_parent_idx": {"name": "_customers_v_version_industry_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_customers_v_version_industry_parent_fk": {"name": "_customers_v_version_industry_parent_fk", "tableFrom": "_customers_v_version_industry", "tableTo": "_customers_v", "schemaTo": "cms-payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload._customers_v_version_supabase_products": {"name": "_customers_v_version_supabase_products", "schema": "cms-payload", "columns": {"order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "enum__customers_v_version_supabase_products", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}}, "indexes": {"_customers_v_version_supabase_products_order_idx": {"name": "_customers_v_version_supabase_products_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_customers_v_version_supabase_products_parent_idx": {"name": "_customers_v_version_supabase_products_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_customers_v_version_supabase_products_parent_fk": {"name": "_customers_v_version_supabase_products_parent_fk", "tableFrom": "_customers_v_version_supabase_products", "tableTo": "_customers_v", "schemaTo": "cms-payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload._customers_v": {"name": "_customers_v", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_name": {"name": "version_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_title": {"name": "version_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_slug": {"name": "version_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_slug_lock": {"name": "version_slug_lock", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "version_content": {"name": "version_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "version_description": {"name": "version_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_about": {"name": "version_about", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_company_url": {"name": "version_company_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_company_size": {"name": "version_company_size", "type": "enum__customers_v_version_company_size", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false}, "version_region": {"name": "version_region", "type": "enum__customers_v_version_region", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false}, "version_meta_title": {"name": "version_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_meta_image_id": {"name": "version_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_meta_description": {"name": "version_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_published_at": {"name": "version_published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_logo_id": {"name": "version_logo_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_logo_inverse_id": {"name": "version_logo_inverse_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version__status": {"name": "version__status", "type": "enum__customers_v_version_status", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "latest": {"name": "latest", "type": "boolean", "primaryKey": false, "notNull": false}, "autosave": {"name": "autosave", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"_customers_v_parent_idx": {"name": "_customers_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_customers_v_version_version_slug_idx": {"name": "_customers_v_version_version_slug_idx", "columns": [{"expression": "version_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_customers_v_version_meta_version_meta_image_idx": {"name": "_customers_v_version_meta_version_meta_image_idx", "columns": [{"expression": "version_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_customers_v_version_version_logo_idx": {"name": "_customers_v_version_version_logo_idx", "columns": [{"expression": "version_logo_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_customers_v_version_version_logo_inverse_idx": {"name": "_customers_v_version_version_logo_inverse_idx", "columns": [{"expression": "version_logo_inverse_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_customers_v_version_version_updated_at_idx": {"name": "_customers_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_customers_v_version_version_created_at_idx": {"name": "_customers_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_customers_v_version_version__status_idx": {"name": "_customers_v_version_version__status_idx", "columns": [{"expression": "version__status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_customers_v_created_at_idx": {"name": "_customers_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_customers_v_updated_at_idx": {"name": "_customers_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_customers_v_latest_idx": {"name": "_customers_v_latest_idx", "columns": [{"expression": "latest", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_customers_v_autosave_idx": {"name": "_customers_v_autosave_idx", "columns": [{"expression": "autosave", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_customers_v_parent_id_customers_id_fk": {"name": "_customers_v_parent_id_customers_id_fk", "tableFrom": "_customers_v", "tableTo": "customers", "schemaTo": "cms-payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_customers_v_version_meta_image_id_media_id_fk": {"name": "_customers_v_version_meta_image_id_media_id_fk", "tableFrom": "_customers_v", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["version_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_customers_v_version_logo_id_media_id_fk": {"name": "_customers_v_version_logo_id_media_id_fk", "tableFrom": "_customers_v", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["version_logo_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_customers_v_version_logo_inverse_id_media_id_fk": {"name": "_customers_v_version_logo_inverse_id_media_id_fk", "tableFrom": "_customers_v", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["version_logo_inverse_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.events_type": {"name": "events_type", "schema": "cms-payload", "columns": {"order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "enum_events_type", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}}, "indexes": {"events_type_order_idx": {"name": "events_type_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_type_parent_idx": {"name": "events_type_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"events_type_parent_fk": {"name": "events_type_parent_fk", "tableFrom": "events_type", "tableTo": "events", "schemaTo": "cms-payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.events": {"name": "events", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug_lock": {"name": "slug_lock", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "subtitle": {"name": "subtitle", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": false}, "thumb_id": {"name": "thumb_id", "type": "integer", "primaryKey": false, "notNull": false}, "image_id": {"name": "image_id", "type": "integer", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "timezone": {"name": "timezone", "type": "enum_events_timezone", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false}, "show_end_date": {"name": "show_end_date", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "end_date": {"name": "end_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "on_demand": {"name": "on_demand", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "disable_page_build": {"name": "disable_page_build", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "link_href": {"name": "link_href", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_target": {"name": "link_target", "type": "enum_events_link_target", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false, "default": "'_blank'"}, "main_cta_href": {"name": "main_cta_href", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "main_cta_target": {"name": "main_cta_target", "type": "enum_events_main_cta_target", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false, "default": "'_blank'"}, "main_cta_label": {"name": "main_cta_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "main_cta_disabled": {"name": "main_cta_disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "main_cta_disabled_label": {"name": "main_cta_disabled_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "company_show_company": {"name": "company_show_company", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "company_name": {"name": "company_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "company_website_url": {"name": "company_website_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "company_logo_id": {"name": "company_logo_id", "type": "integer", "primaryKey": false, "notNull": false}, "company_logo_light_id": {"name": "company_logo_light_id", "type": "integer", "primaryKey": false, "notNull": false}, "participants_show_participants": {"name": "participants_show_participants", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "meta_title": {"name": "meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_image_id": {"name": "meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_description": {"name": "meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "_status": {"name": "_status", "type": "enum_events_status", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false, "default": "'draft'"}}, "indexes": {"events_slug_idx": {"name": "events_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_thumb_idx": {"name": "events_thumb_idx", "columns": [{"expression": "thumb_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_image_idx": {"name": "events_image_idx", "columns": [{"expression": "image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_company_company_logo_idx": {"name": "events_company_company_logo_idx", "columns": [{"expression": "company_logo_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_company_company_logo_light_idx": {"name": "events_company_company_logo_light_idx", "columns": [{"expression": "company_logo_light_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_meta_meta_image_idx": {"name": "events_meta_meta_image_idx", "columns": [{"expression": "meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_updated_at_idx": {"name": "events_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_created_at_idx": {"name": "events_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events__status_idx": {"name": "events__status_idx", "columns": [{"expression": "_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"events_thumb_id_media_id_fk": {"name": "events_thumb_id_media_id_fk", "tableFrom": "events", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["thumb_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "events_image_id_media_id_fk": {"name": "events_image_id_media_id_fk", "tableFrom": "events", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "events_company_logo_id_media_id_fk": {"name": "events_company_logo_id_media_id_fk", "tableFrom": "events", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["company_logo_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "events_company_logo_light_id_media_id_fk": {"name": "events_company_logo_light_id_media_id_fk", "tableFrom": "events", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["company_logo_light_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "events_meta_image_id_media_id_fk": {"name": "events_meta_image_id_media_id_fk", "tableFrom": "events", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.events_rels": {"name": "events_rels", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "authors_id": {"name": "authors_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"events_rels_order_idx": {"name": "events_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_rels_parent_idx": {"name": "events_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_rels_path_idx": {"name": "events_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_rels_authors_id_idx": {"name": "events_rels_authors_id_idx", "columns": [{"expression": "authors_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"events_rels_parent_fk": {"name": "events_rels_parent_fk", "tableFrom": "events_rels", "tableTo": "events", "schemaTo": "cms-payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "events_rels_authors_fk": {"name": "events_rels_authors_fk", "tableFrom": "events_rels", "tableTo": "authors", "schemaTo": "cms-payload", "columnsFrom": ["authors_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload._events_v_version_type": {"name": "_events_v_version_type", "schema": "cms-payload", "columns": {"order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "enum__events_v_version_type", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}}, "indexes": {"_events_v_version_type_order_idx": {"name": "_events_v_version_type_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_events_v_version_type_parent_idx": {"name": "_events_v_version_type_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_events_v_version_type_parent_fk": {"name": "_events_v_version_type_parent_fk", "tableFrom": "_events_v_version_type", "tableTo": "_events_v", "schemaTo": "cms-payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload._events_v": {"name": "_events_v", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_title": {"name": "version_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_slug": {"name": "version_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_slug_lock": {"name": "version_slug_lock", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "version_subtitle": {"name": "version_subtitle", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_content": {"name": "version_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "version_thumb_id": {"name": "version_thumb_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_image_id": {"name": "version_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_date": {"name": "version_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_timezone": {"name": "version_timezone", "type": "enum__events_v_version_timezone", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false}, "version_show_end_date": {"name": "version_show_end_date", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "version_end_date": {"name": "version_end_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_duration": {"name": "version_duration", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_on_demand": {"name": "version_on_demand", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "version_disable_page_build": {"name": "version_disable_page_build", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "version_link_href": {"name": "version_link_href", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_link_target": {"name": "version_link_target", "type": "enum__events_v_version_link_target", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false, "default": "'_blank'"}, "version_main_cta_href": {"name": "version_main_cta_href", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_main_cta_target": {"name": "version_main_cta_target", "type": "enum__events_v_version_main_cta_target", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false, "default": "'_blank'"}, "version_main_cta_label": {"name": "version_main_cta_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_main_cta_disabled": {"name": "version_main_cta_disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "version_main_cta_disabled_label": {"name": "version_main_cta_disabled_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_company_show_company": {"name": "version_company_show_company", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "version_company_name": {"name": "version_company_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_company_website_url": {"name": "version_company_website_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_company_logo_id": {"name": "version_company_logo_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_company_logo_light_id": {"name": "version_company_logo_light_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_participants_show_participants": {"name": "version_participants_show_participants", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "version_meta_title": {"name": "version_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_meta_image_id": {"name": "version_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_meta_description": {"name": "version_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_published_at": {"name": "version_published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version__status": {"name": "version__status", "type": "enum__events_v_version_status", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "latest": {"name": "latest", "type": "boolean", "primaryKey": false, "notNull": false}, "autosave": {"name": "autosave", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"_events_v_parent_idx": {"name": "_events_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_events_v_version_version_slug_idx": {"name": "_events_v_version_version_slug_idx", "columns": [{"expression": "version_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_events_v_version_version_thumb_idx": {"name": "_events_v_version_version_thumb_idx", "columns": [{"expression": "version_thumb_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_events_v_version_version_image_idx": {"name": "_events_v_version_version_image_idx", "columns": [{"expression": "version_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_events_v_version_company_version_company_logo_idx": {"name": "_events_v_version_company_version_company_logo_idx", "columns": [{"expression": "version_company_logo_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_events_v_version_company_version_company_logo_light_idx": {"name": "_events_v_version_company_version_company_logo_light_idx", "columns": [{"expression": "version_company_logo_light_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_events_v_version_meta_version_meta_image_idx": {"name": "_events_v_version_meta_version_meta_image_idx", "columns": [{"expression": "version_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_events_v_version_version_updated_at_idx": {"name": "_events_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_events_v_version_version_created_at_idx": {"name": "_events_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_events_v_version_version__status_idx": {"name": "_events_v_version_version__status_idx", "columns": [{"expression": "version__status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_events_v_created_at_idx": {"name": "_events_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_events_v_updated_at_idx": {"name": "_events_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_events_v_latest_idx": {"name": "_events_v_latest_idx", "columns": [{"expression": "latest", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_events_v_autosave_idx": {"name": "_events_v_autosave_idx", "columns": [{"expression": "autosave", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_events_v_parent_id_events_id_fk": {"name": "_events_v_parent_id_events_id_fk", "tableFrom": "_events_v", "tableTo": "events", "schemaTo": "cms-payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_events_v_version_thumb_id_media_id_fk": {"name": "_events_v_version_thumb_id_media_id_fk", "tableFrom": "_events_v", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["version_thumb_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_events_v_version_image_id_media_id_fk": {"name": "_events_v_version_image_id_media_id_fk", "tableFrom": "_events_v", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["version_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_events_v_version_company_logo_id_media_id_fk": {"name": "_events_v_version_company_logo_id_media_id_fk", "tableFrom": "_events_v", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["version_company_logo_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_events_v_version_company_logo_light_id_media_id_fk": {"name": "_events_v_version_company_logo_light_id_media_id_fk", "tableFrom": "_events_v", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["version_company_logo_light_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_events_v_version_meta_image_id_media_id_fk": {"name": "_events_v_version_meta_image_id_media_id_fk", "tableFrom": "_events_v", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["version_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload._events_v_rels": {"name": "_events_v_rels", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "authors_id": {"name": "authors_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"_events_v_rels_order_idx": {"name": "_events_v_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_events_v_rels_parent_idx": {"name": "_events_v_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_events_v_rels_path_idx": {"name": "_events_v_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_events_v_rels_authors_id_idx": {"name": "_events_v_rels_authors_id_idx", "columns": [{"expression": "authors_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_events_v_rels_parent_fk": {"name": "_events_v_rels_parent_fk", "tableFrom": "_events_v_rels", "tableTo": "_events_v", "schemaTo": "cms-payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_events_v_rels_authors_fk": {"name": "_events_v_rels_authors_fk", "tableFrom": "_events_v_rels", "tableTo": "authors", "schemaTo": "cms-payload", "columnsFrom": ["authors_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.media": {"name": "media", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "alt": {"name": "alt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "caption": {"name": "caption", "type": "jsonb", "primaryKey": false, "notNull": false}, "prefix": {"name": "prefix", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'media'"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "thumbnail_u_r_l": {"name": "thumbnail_u_r_l", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filename": {"name": "filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filesize": {"name": "filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "height": {"name": "height", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_x": {"name": "focal_x", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_y": {"name": "focal_y", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_url": {"name": "sizes_thumbnail_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_thumbnail_width": {"name": "sizes_thumbnail_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_height": {"name": "sizes_thumbnail_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_mime_type": {"name": "sizes_thumbnail_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_thumbnail_filesize": {"name": "sizes_thumbnail_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_filename": {"name": "sizes_thumbnail_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_square_url": {"name": "sizes_square_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_square_width": {"name": "sizes_square_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_square_height": {"name": "sizes_square_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_square_mime_type": {"name": "sizes_square_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_square_filesize": {"name": "sizes_square_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_square_filename": {"name": "sizes_square_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_small_url": {"name": "sizes_small_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_small_width": {"name": "sizes_small_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_small_height": {"name": "sizes_small_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_small_mime_type": {"name": "sizes_small_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_small_filesize": {"name": "sizes_small_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_small_filename": {"name": "sizes_small_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_medium_url": {"name": "sizes_medium_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_medium_width": {"name": "sizes_medium_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_medium_height": {"name": "sizes_medium_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_medium_mime_type": {"name": "sizes_medium_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_medium_filesize": {"name": "sizes_medium_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_medium_filename": {"name": "sizes_medium_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_large_url": {"name": "sizes_large_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_large_width": {"name": "sizes_large_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_large_height": {"name": "sizes_large_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_large_mime_type": {"name": "sizes_large_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_large_filesize": {"name": "sizes_large_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_large_filename": {"name": "sizes_large_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_xlarge_url": {"name": "sizes_xlarge_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_xlarge_width": {"name": "sizes_xlarge_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_xlarge_height": {"name": "sizes_xlarge_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_xlarge_mime_type": {"name": "sizes_xlarge_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_xlarge_filesize": {"name": "sizes_xlarge_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_xlarge_filename": {"name": "sizes_xlarge_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_og_url": {"name": "sizes_og_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_og_width": {"name": "sizes_og_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_og_height": {"name": "sizes_og_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_og_mime_type": {"name": "sizes_og_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_og_filesize": {"name": "sizes_og_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_og_filename": {"name": "sizes_og_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"media_updated_at_idx": {"name": "media_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_created_at_idx": {"name": "media_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_filename_idx": {"name": "media_filename_idx", "columns": [{"expression": "filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_thumbnail_sizes_thumbnail_filename_idx": {"name": "media_sizes_thumbnail_sizes_thumbnail_filename_idx", "columns": [{"expression": "sizes_thumbnail_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_square_sizes_square_filename_idx": {"name": "media_sizes_square_sizes_square_filename_idx", "columns": [{"expression": "sizes_square_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_small_sizes_small_filename_idx": {"name": "media_sizes_small_sizes_small_filename_idx", "columns": [{"expression": "sizes_small_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_medium_sizes_medium_filename_idx": {"name": "media_sizes_medium_sizes_medium_filename_idx", "columns": [{"expression": "sizes_medium_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_large_sizes_large_filename_idx": {"name": "media_sizes_large_sizes_large_filename_idx", "columns": [{"expression": "sizes_large_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_xlarge_sizes_xlarge_filename_idx": {"name": "media_sizes_xlarge_sizes_xlarge_filename_idx", "columns": [{"expression": "sizes_xlarge_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_og_sizes_og_filename_idx": {"name": "media_sizes_og_sizes_og_filename_idx", "columns": [{"expression": "sizes_og_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.posts": {"name": "posts", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug_lock": {"name": "slug_lock", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": false}, "thumb_id": {"name": "thumb_id", "type": "integer", "primaryKey": false, "notNull": false}, "image_id": {"name": "image_id", "type": "integer", "primaryKey": false, "notNull": false}, "launchweek": {"name": "launchweek", "type": "enum_posts_launchweek", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false}, "reading_time": {"name": "reading_time", "type": "numeric", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "toc_depth": {"name": "toc_depth", "type": "numeric", "primaryKey": false, "notNull": false, "default": 2}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_title": {"name": "meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_image_id": {"name": "meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_description": {"name": "meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "_status": {"name": "_status", "type": "enum_posts_status", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false, "default": "'draft'"}}, "indexes": {"posts_slug_idx": {"name": "posts_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_thumb_idx": {"name": "posts_thumb_idx", "columns": [{"expression": "thumb_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_image_idx": {"name": "posts_image_idx", "columns": [{"expression": "image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_meta_meta_image_idx": {"name": "posts_meta_meta_image_idx", "columns": [{"expression": "meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_updated_at_idx": {"name": "posts_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_created_at_idx": {"name": "posts_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts__status_idx": {"name": "posts__status_idx", "columns": [{"expression": "_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"posts_thumb_id_media_id_fk": {"name": "posts_thumb_id_media_id_fk", "tableFrom": "posts", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["thumb_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "posts_image_id_media_id_fk": {"name": "posts_image_id_media_id_fk", "tableFrom": "posts", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "posts_meta_image_id_media_id_fk": {"name": "posts_meta_image_id_media_id_fk", "tableFrom": "posts", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.posts_rels": {"name": "posts_rels", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "categories_id": {"name": "categories_id", "type": "integer", "primaryKey": false, "notNull": false}, "authors_id": {"name": "authors_id", "type": "integer", "primaryKey": false, "notNull": false}, "tags_id": {"name": "tags_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"posts_rels_order_idx": {"name": "posts_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_rels_parent_idx": {"name": "posts_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_rels_path_idx": {"name": "posts_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_rels_categories_id_idx": {"name": "posts_rels_categories_id_idx", "columns": [{"expression": "categories_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_rels_authors_id_idx": {"name": "posts_rels_authors_id_idx", "columns": [{"expression": "authors_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_rels_tags_id_idx": {"name": "posts_rels_tags_id_idx", "columns": [{"expression": "tags_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"posts_rels_parent_fk": {"name": "posts_rels_parent_fk", "tableFrom": "posts_rels", "tableTo": "posts", "schemaTo": "cms-payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "posts_rels_categories_fk": {"name": "posts_rels_categories_fk", "tableFrom": "posts_rels", "tableTo": "categories", "schemaTo": "cms-payload", "columnsFrom": ["categories_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "posts_rels_authors_fk": {"name": "posts_rels_authors_fk", "tableFrom": "posts_rels", "tableTo": "authors", "schemaTo": "cms-payload", "columnsFrom": ["authors_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "posts_rels_tags_fk": {"name": "posts_rels_tags_fk", "tableFrom": "posts_rels", "tableTo": "tags", "schemaTo": "cms-payload", "columnsFrom": ["tags_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload._posts_v": {"name": "_posts_v", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_title": {"name": "version_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_slug": {"name": "version_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_slug_lock": {"name": "version_slug_lock", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "version_content": {"name": "version_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "version_thumb_id": {"name": "version_thumb_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_image_id": {"name": "version_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_launchweek": {"name": "version_launchweek", "type": "enum__posts_v_version_launchweek", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false}, "version_reading_time": {"name": "version_reading_time", "type": "numeric", "primaryKey": false, "notNull": false}, "version_date": {"name": "version_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_toc_depth": {"name": "version_toc_depth", "type": "numeric", "primaryKey": false, "notNull": false, "default": 2}, "version_description": {"name": "version_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_meta_title": {"name": "version_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_meta_image_id": {"name": "version_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_meta_description": {"name": "version_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_published_at": {"name": "version_published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version__status": {"name": "version__status", "type": "enum__posts_v_version_status", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "latest": {"name": "latest", "type": "boolean", "primaryKey": false, "notNull": false}, "autosave": {"name": "autosave", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"_posts_v_parent_idx": {"name": "_posts_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_slug_idx": {"name": "_posts_v_version_version_slug_idx", "columns": [{"expression": "version_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_thumb_idx": {"name": "_posts_v_version_version_thumb_idx", "columns": [{"expression": "version_thumb_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_image_idx": {"name": "_posts_v_version_version_image_idx", "columns": [{"expression": "version_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_meta_version_meta_image_idx": {"name": "_posts_v_version_meta_version_meta_image_idx", "columns": [{"expression": "version_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_updated_at_idx": {"name": "_posts_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_created_at_idx": {"name": "_posts_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version__status_idx": {"name": "_posts_v_version_version__status_idx", "columns": [{"expression": "version__status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_created_at_idx": {"name": "_posts_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_updated_at_idx": {"name": "_posts_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_latest_idx": {"name": "_posts_v_latest_idx", "columns": [{"expression": "latest", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_autosave_idx": {"name": "_posts_v_autosave_idx", "columns": [{"expression": "autosave", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_posts_v_parent_id_posts_id_fk": {"name": "_posts_v_parent_id_posts_id_fk", "tableFrom": "_posts_v", "tableTo": "posts", "schemaTo": "cms-payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_posts_v_version_thumb_id_media_id_fk": {"name": "_posts_v_version_thumb_id_media_id_fk", "tableFrom": "_posts_v", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["version_thumb_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_posts_v_version_image_id_media_id_fk": {"name": "_posts_v_version_image_id_media_id_fk", "tableFrom": "_posts_v", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["version_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_posts_v_version_meta_image_id_media_id_fk": {"name": "_posts_v_version_meta_image_id_media_id_fk", "tableFrom": "_posts_v", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["version_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload._posts_v_rels": {"name": "_posts_v_rels", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "categories_id": {"name": "categories_id", "type": "integer", "primaryKey": false, "notNull": false}, "authors_id": {"name": "authors_id", "type": "integer", "primaryKey": false, "notNull": false}, "tags_id": {"name": "tags_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"_posts_v_rels_order_idx": {"name": "_posts_v_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_rels_parent_idx": {"name": "_posts_v_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_rels_path_idx": {"name": "_posts_v_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_rels_categories_id_idx": {"name": "_posts_v_rels_categories_id_idx", "columns": [{"expression": "categories_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_rels_authors_id_idx": {"name": "_posts_v_rels_authors_id_idx", "columns": [{"expression": "authors_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_rels_tags_id_idx": {"name": "_posts_v_rels_tags_id_idx", "columns": [{"expression": "tags_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_posts_v_rels_parent_fk": {"name": "_posts_v_rels_parent_fk", "tableFrom": "_posts_v_rels", "tableTo": "_posts_v", "schemaTo": "cms-payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_posts_v_rels_categories_fk": {"name": "_posts_v_rels_categories_fk", "tableFrom": "_posts_v_rels", "tableTo": "categories", "schemaTo": "cms-payload", "columnsFrom": ["categories_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_posts_v_rels_authors_fk": {"name": "_posts_v_rels_authors_fk", "tableFrom": "_posts_v_rels", "tableTo": "authors", "schemaTo": "cms-payload", "columnsFrom": ["authors_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_posts_v_rels_tags_fk": {"name": "_posts_v_rels_tags_fk", "tableFrom": "_posts_v_rels", "tableTo": "tags", "schemaTo": "cms-payload", "columnsFrom": ["tags_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.tags": {"name": "tags", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"tags_updated_at_idx": {"name": "tags_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "tags_created_at_idx": {"name": "tags_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.users_roles": {"name": "users_roles", "schema": "cms-payload", "columns": {"order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "enum_users_roles", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}}, "indexes": {"users_roles_order_idx": {"name": "users_roles_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_roles_parent_idx": {"name": "users_roles_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"users_roles_parent_fk": {"name": "users_roles_parent_fk", "tableFrom": "users_roles", "tableTo": "users", "schemaTo": "cms-payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.users": {"name": "users", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "reset_password_token": {"name": "reset_password_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "reset_password_expiration": {"name": "reset_password_expiration", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "salt": {"name": "salt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "hash": {"name": "hash", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "login_attempts": {"name": "login_attempts", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "lock_until": {"name": "lock_until", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"users_updated_at_idx": {"name": "users_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_created_at_idx": {"name": "users_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.payload_jobs_log": {"name": "payload_jobs_log", "schema": "cms-payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "executed_at": {"name": "executed_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true}, "completed_at": {"name": "completed_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true}, "task_slug": {"name": "task_slug", "type": "enum_payload_jobs_log_task_slug", "typeSchema": "cms-payload", "primaryKey": false, "notNull": true}, "task_i_d": {"name": "task_i_d", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "input": {"name": "input", "type": "jsonb", "primaryKey": false, "notNull": false}, "output": {"name": "output", "type": "jsonb", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "enum_payload_jobs_log_state", "typeSchema": "cms-payload", "primaryKey": false, "notNull": true}, "error": {"name": "error", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"payload_jobs_log_order_idx": {"name": "payload_jobs_log_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_log_parent_id_idx": {"name": "payload_jobs_log_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_jobs_log_parent_id_fk": {"name": "payload_jobs_log_parent_id_fk", "tableFrom": "payload_jobs_log", "tableTo": "payload_jobs", "schemaTo": "cms-payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.payload_jobs": {"name": "payload_jobs", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "input": {"name": "input", "type": "jsonb", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "total_tried": {"name": "total_tried", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "has_error": {"name": "has_error", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "error": {"name": "error", "type": "jsonb", "primaryKey": false, "notNull": false}, "task_slug": {"name": "task_slug", "type": "enum_payload_jobs_task_slug", "typeSchema": "cms-payload", "primaryKey": false, "notNull": false}, "queue": {"name": "queue", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'default'"}, "wait_until": {"name": "wait_until", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "processing": {"name": "processing", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_jobs_completed_at_idx": {"name": "payload_jobs_completed_at_idx", "columns": [{"expression": "completed_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_total_tried_idx": {"name": "payload_jobs_total_tried_idx", "columns": [{"expression": "total_tried", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_has_error_idx": {"name": "payload_jobs_has_error_idx", "columns": [{"expression": "has_error", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_task_slug_idx": {"name": "payload_jobs_task_slug_idx", "columns": [{"expression": "task_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_queue_idx": {"name": "payload_jobs_queue_idx", "columns": [{"expression": "queue", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_wait_until_idx": {"name": "payload_jobs_wait_until_idx", "columns": [{"expression": "wait_until", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_processing_idx": {"name": "payload_jobs_processing_idx", "columns": [{"expression": "processing", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_updated_at_idx": {"name": "payload_jobs_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_created_at_idx": {"name": "payload_jobs_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.payload_locked_documents": {"name": "payload_locked_documents", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "global_slug": {"name": "global_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_locked_documents_global_slug_idx": {"name": "payload_locked_documents_global_slug_idx", "columns": [{"expression": "global_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_updated_at_idx": {"name": "payload_locked_documents_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_created_at_idx": {"name": "payload_locked_documents_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.payload_locked_documents_rels": {"name": "payload_locked_documents_rels", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "authors_id": {"name": "authors_id", "type": "integer", "primaryKey": false, "notNull": false}, "categories_id": {"name": "categories_id", "type": "integer", "primaryKey": false, "notNull": false}, "customers_id": {"name": "customers_id", "type": "integer", "primaryKey": false, "notNull": false}, "events_id": {"name": "events_id", "type": "integer", "primaryKey": false, "notNull": false}, "media_id": {"name": "media_id", "type": "integer", "primaryKey": false, "notNull": false}, "posts_id": {"name": "posts_id", "type": "integer", "primaryKey": false, "notNull": false}, "tags_id": {"name": "tags_id", "type": "integer", "primaryKey": false, "notNull": false}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}, "payload_jobs_id": {"name": "payload_jobs_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"payload_locked_documents_rels_order_idx": {"name": "payload_locked_documents_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_parent_idx": {"name": "payload_locked_documents_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_path_idx": {"name": "payload_locked_documents_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_authors_id_idx": {"name": "payload_locked_documents_rels_authors_id_idx", "columns": [{"expression": "authors_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_categories_id_idx": {"name": "payload_locked_documents_rels_categories_id_idx", "columns": [{"expression": "categories_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_customers_id_idx": {"name": "payload_locked_documents_rels_customers_id_idx", "columns": [{"expression": "customers_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_events_id_idx": {"name": "payload_locked_documents_rels_events_id_idx", "columns": [{"expression": "events_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_media_id_idx": {"name": "payload_locked_documents_rels_media_id_idx", "columns": [{"expression": "media_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_posts_id_idx": {"name": "payload_locked_documents_rels_posts_id_idx", "columns": [{"expression": "posts_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_tags_id_idx": {"name": "payload_locked_documents_rels_tags_id_idx", "columns": [{"expression": "tags_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_users_id_idx": {"name": "payload_locked_documents_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_payload_jobs_id_idx": {"name": "payload_locked_documents_rels_payload_jobs_id_idx", "columns": [{"expression": "payload_jobs_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_locked_documents_rels_parent_fk": {"name": "payload_locked_documents_rels_parent_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "payload_locked_documents", "schemaTo": "cms-payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_authors_fk": {"name": "payload_locked_documents_rels_authors_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "authors", "schemaTo": "cms-payload", "columnsFrom": ["authors_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_categories_fk": {"name": "payload_locked_documents_rels_categories_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "categories", "schemaTo": "cms-payload", "columnsFrom": ["categories_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_customers_fk": {"name": "payload_locked_documents_rels_customers_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "customers", "schemaTo": "cms-payload", "columnsFrom": ["customers_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_events_fk": {"name": "payload_locked_documents_rels_events_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "events", "schemaTo": "cms-payload", "columnsFrom": ["events_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_media_fk": {"name": "payload_locked_documents_rels_media_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "media", "schemaTo": "cms-payload", "columnsFrom": ["media_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_posts_fk": {"name": "payload_locked_documents_rels_posts_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "posts", "schemaTo": "cms-payload", "columnsFrom": ["posts_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_tags_fk": {"name": "payload_locked_documents_rels_tags_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "tags", "schemaTo": "cms-payload", "columnsFrom": ["tags_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_users_fk": {"name": "payload_locked_documents_rels_users_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "users", "schemaTo": "cms-payload", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_payload_jobs_fk": {"name": "payload_locked_documents_rels_payload_jobs_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "payload_jobs", "schemaTo": "cms-payload", "columnsFrom": ["payload_jobs_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.payload_preferences": {"name": "payload_preferences", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_preferences_key_idx": {"name": "payload_preferences_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_updated_at_idx": {"name": "payload_preferences_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_created_at_idx": {"name": "payload_preferences_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.payload_preferences_rels": {"name": "payload_preferences_rels", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"payload_preferences_rels_order_idx": {"name": "payload_preferences_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_parent_idx": {"name": "payload_preferences_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_path_idx": {"name": "payload_preferences_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_users_id_idx": {"name": "payload_preferences_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_preferences_rels_parent_fk": {"name": "payload_preferences_rels_parent_fk", "tableFrom": "payload_preferences_rels", "tableTo": "payload_preferences", "schemaTo": "cms-payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_preferences_rels_users_fk": {"name": "payload_preferences_rels_users_fk", "tableFrom": "payload_preferences_rels", "tableTo": "users", "schemaTo": "cms-payload", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "cms-payload.payload_migrations": {"name": "payload_migrations", "schema": "cms-payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "batch": {"name": "batch", "type": "numeric", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_migrations_updated_at_idx": {"name": "payload_migrations_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_migrations_created_at_idx": {"name": "payload_migrations_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"cms-payload.enum_customers_industry": {"name": "enum_customers_industry", "schema": "cms-payload", "values": ["healthcare", "fintech", "ecommerce", "education", "gaming", "media", "real-estate", "saas", "social", "analytics", "ai", "developer-tools"]}, "cms-payload.enum_customers_supabase_products": {"name": "enum_customers_supabase_products", "schema": "cms-payload", "values": ["database", "auth", "storage", "realtime", "functions", "vector"]}, "cms-payload.enum_customers_company_size": {"name": "enum_customers_company_size", "schema": "cms-payload", "values": ["startup", "enterprise", "indie_dev"]}, "cms-payload.enum_customers_region": {"name": "enum_customers_region", "schema": "cms-payload", "values": ["Asia", "Europe", "North America", "South America", "Africa", "Oceania"]}, "cms-payload.enum_customers_status": {"name": "enum_customers_status", "schema": "cms-payload", "values": ["draft", "published"]}, "cms-payload.enum__customers_v_version_industry": {"name": "enum__customers_v_version_industry", "schema": "cms-payload", "values": ["healthcare", "fintech", "ecommerce", "education", "gaming", "media", "real-estate", "saas", "social", "analytics", "ai", "developer-tools"]}, "cms-payload.enum__customers_v_version_supabase_products": {"name": "enum__customers_v_version_supabase_products", "schema": "cms-payload", "values": ["database", "auth", "storage", "realtime", "functions", "vector"]}, "cms-payload.enum__customers_v_version_company_size": {"name": "enum__customers_v_version_company_size", "schema": "cms-payload", "values": ["startup", "enterprise", "indie_dev"]}, "cms-payload.enum__customers_v_version_region": {"name": "enum__customers_v_version_region", "schema": "cms-payload", "values": ["Asia", "Europe", "North America", "South America", "Africa", "Oceania"]}, "cms-payload.enum__customers_v_version_status": {"name": "enum__customers_v_version_status", "schema": "cms-payload", "values": ["draft", "published"]}, "cms-payload.enum_events_type": {"name": "enum_events_type", "schema": "cms-payload", "values": ["conference", "hackathon", "launch-week", "meetup", "office-hours", "talk", "webinar", "workshop", "other"]}, "cms-payload.enum_events_timezone": {"name": "enum_events_timezone", "schema": "cms-payload", "values": ["Africa/Abidjan", "Africa/Accra", "Africa/Addis_Ababa", "Africa/Algiers", "Africa/Asmara", "Africa/Bamako", "Africa/Bangui", "Africa/Banjul", "Africa/Bissau", "Africa/Blantyre", "Africa/Brazzaville", "Africa/Bujumbura", "Africa/Cairo", "Africa/Casablanca", "Africa/Ceuta", "Africa/Conakry", "Africa/Dakar", "Africa/Dar_es_Salaam", "Africa/Djibouti", "Africa/Douala", "Africa/El_Aaiun", "Africa/Freetown", "Africa/Gaborone", "Africa/Harare", "Africa/Johannesburg", "Africa/Juba", "Africa/Kampala", "Africa/Khartoum", "Africa/Kigali", "Africa/Kinshasa", "Africa/Lagos", "Africa/Libreville", "Africa/Lome", "Africa/Luanda", "Africa/Lubumbashi", "Africa/Lusaka", "Africa/Malabo", "Africa/Maputo", "Africa/Maseru", "Africa/Mbabane", "Africa/Mogadishu", "Africa/Monrovia", "Africa/Nairobi", "Africa/Ndjamena", "Africa/Niamey", "Africa/Nouakchott", "Africa/Ouagadougou", "Africa/Porto-Novo", "Africa/Sao_Tome", "Africa/Tripoli", "Africa/Tunis", "Africa/Windhoek", "America/Adak", "America/Anchorage", "America/Anguilla", "America/Antigua", "America/Araguaina", "America/Argentina/Buenos_Aires", "America/Argentina/Catamarca", "America/Argentina/Cordoba", "America/Argentina/Jujuy", "America/Argentina/La_Rioja", "America/Argentina/Mendoza", "America/Argentina/Rio_Gallegos", "America/Argentina/Salta", "America/Argentina/San_Juan", "America/Argentina/San_Luis", "America/Argentina/Tucuman", "America/Argentina/Ushuaia", "America/Aruba", "America/Asuncion", "America/Atikokan", "America/Bahia", "America/Bahia_Banderas", "America/Barbados", "America/Belem", "America/Belize", "America/Blanc-Sablon", "America/Boa_Vista", "America/Bogota", "America/Boise", "America/Cambridge_Bay", "America/Campo_Grande", "America/Cancun", "America/Caracas", "America/Cayenne", "America/Cayman", "America/Chicago", "America/Chihuahua", "America/Costa_Rica", "America/Creston", "America/Cuiaba", "America/Curacao", "America/Danmarkshavn", "America/Dawson", "America/Dawson_Creek", "America/Denver", "America/Detroit", "America/Dominica", "America/Edmonton", "America/Eirunepe", "America/El_Salvador", "America/Fort_Nelson", "America/Fortaleza", "America/Glace_Bay", "America/Godthab", "America/Goose_Bay", "America/Grand_Turk", "America/Grenada", "America/Guadeloupe", "America/Guatemala", "America/Guayaquil", "America/Guyana", "America/Halifax", "America/Havana", "America/Hermosillo", "America/Indiana/Indianapolis", "America/Indiana/Knox", "America/Indiana/Marengo", "America/Indiana/Petersburg", "America/Indiana/Tell_City", "America/Indiana/Vevay", "America/Indiana/Vincennes", "America/Indiana/Winamac", "America/Inuvik", "America/Iqaluit", "America/Jamaica", "America/Juneau", "America/Kentucky/Louisville", "America/Kentucky/Monticello", "America/Kralendijk", "America/La_Paz", "America/Lima", "America/Los_Angeles", "America/Lower_Princes", "America/Maceio", "America/Managua", "America/Manaus", "America/Marigot", "America/Martinique", "America/Matamoros", "America/Mazatlan", "America/Menominee", "America/Merida", "America/Metlakatla", "America/Mexico_City", "America/Miquelon", "America/Moncton", "America/Monterrey", "America/Montevideo", "America/Montserrat", "America/Nassau", "America/New_York", "America/Nipigon", "America/Nome", "America/Noronha", "America/North_Dakota/Beulah", "America/North_Dakota/Center", "America/North_Dakota/New_Salem", "America/Ojinaga", "America/Panama", "America/Pangnirtung", "America/Paramaribo", "America/Phoenix", "America/Port-au-Prince", "America/Port_of_Spain", "America/Porto_Velho", "America/Puerto_Rico", "America/Punta_Arenas", "America/Rainy_River", "America/Rankin_Inlet", "America/Recife", "America/Regina", "America/Resolute", "America/Rio_Branco", "America/Santarem", "America/Santiago", "America/Santo_Domingo", "America/Sao_Paulo", "America/Scoresbysund", "America/Sitka", "America/St_Barthelemy", "America/St_Johns", "America/St_Kitts", "America/St_Lucia", "America/St_Thomas", "America/St_Vincent", "America/Swift_Current", "America/Tegucigalpa", "America/Thule", "America/Thunder_Bay", "America/Tijuana", "America/Toronto", "America/Tortola", "America/Vancouver", "America/Whitehorse", "America/Winnipeg", "America/Yakutat", "America/Yellowknife", "Antarctica/Casey", "Antarctica/Davis", "Antarctica/DumontDUrville", "Antarctica/Macquarie", "Antarctica/Mawson", "Antarctica/McMurdo", "Antarctica/Palmer", "Antarctica/Rothera", "Antarctica/Syowa", "Antarctica/Troll", "Antarctica/Vostok", "Arctic/Longyearbyen", "Asia/Aden", "Asia/Almaty", "Asia/Amman", "Asia/Anadyr", "Asia/Aqtau", "Asia/Aqtobe", "Asia/Ashgabat", "Asia/Atyrau", "Asia/Baghdad", "Asia/Bahrain", "Asia/Baku", "Asia/Bangkok", "Asia/Barnaul", "Asia/Beirut", "Asia/Bishkek", "Asia/Brunei", "Asia/Chita", "Asia/Choibalsan", "Asia/Colombo", "Asia/Damascus", "Asia/Dhaka", "Asia/Dili", "Asia/Dubai", "Asia/Dushanbe", "Asia/Famagusta", "Asia/Gaza", "Asia/Hebron", "Asia/Ho_Chi_Minh", "Asia/Hong_Kong", "Asia/Hovd", "Asia/Irkutsk", "Asia/Istanbul", "Asia/Jakarta", "Asia/Jayapura", "Asia/Jerusalem", "Asia/Kabul", "Asia/Kamchatka", "Asia/Karachi", "Asia/Kathmandu", "Asia/Khandyga", "Asia/Kolkata", "Asia/Krasnoyarsk", "Asia/Kuala_Lumpur", "Asia/Kuching", "Asia/Kuwait", "Asia/Macau", "Asia/Magadan", "Asia/Makassar", "Asia/Manila", "Asia/Muscat", "Asia/Nicosia", "Asia/Novokuznetsk", "Asia/Novosibirsk", "Asia/Omsk", "Asia/Oral", "Asia/Phnom_Penh", "Asia/Pontianak", "Asia/Pyongyang", "Asia/Qatar", "Asia/Qostanay", "Asia/Qyzylorda", "Asia/Riyadh", "Asia/Sakhalin", "Asia/Samarkand", "Asia/Seoul", "Asia/Shanghai", "Asia/Singapore", "Asia/Srednekolymsk", "Asia/Taipei", "Asia/Tashkent", "Asia/Tbilisi", "Asia/Tehran", "Asia/Thimphu", "Asia/Tokyo", "Asia/Tomsk", "Asia/Ulaanbaatar", "Asia/Urumqi", "Asia/Ust-Nera", "Asia/Vientiane", "Asia/Vladivostok", "Asia/Yakutsk", "Asia/Yangon", "Asia/Yekaterinburg", "Asia/Yerevan", "Atlantic/Azores", "Atlantic/Bermuda", "Atlantic/Canary", "Atlantic/Cape_Verde", "Atlantic/Faroe", "Atlantic/Madeira", "Atlantic/Reykjavik", "Atlantic/South_Georgia", "Atlantic/St_Helena", "Atlantic/Stanley", "Australia/Adelaide", "Australia/Brisbane", "Australia/Broken_Hill", "Australia/Currie", "Australia/Darwin", "Australia/Eucla", "Australia/Hobart", "Australia/Lindeman", "Australia/Lord_Howe", "Australia/Melbourne", "Australia/Perth", "Australia/Sydney", "Europe/Amsterdam", "Europe/Andorra", "Europe/Astrakhan", "Europe/Athens", "Europe/Belgrade", "Europe/Berlin", "Europe/Bratislava", "Europe/Brussels", "Europe/Bucharest", "Europe/Budapest", "Europe/Busingen", "Europe/Chisinau", "Europe/Copenhagen", "Europe/Dublin", "Europe/Gibraltar", "Europe/Guernsey", "Europe/Helsinki", "Europe/Isle_of_Man", "Europe/Istanbul", "Europe/Jersey", "Europe/Kaliningrad", "Europe/Kiev", "Europe/Kirov", "Europe/Lisbon", "Europe/Ljubljana", "Europe/London", "Europe/Luxembourg", "Europe/Madrid", "Europe/Malta", "Europe/Mariehamn", "Europe/Minsk", "Europe/Monaco", "Europe/Moscow", "Europe/Oslo", "Europe/Paris", "Europe/Podgorica", "Europe/Prague", "Europe/Riga", "Europe/Rome", "Europe/Samara", "Europe/San_Marino", "Europe/Sarajevo", "Europe/Saratov", "Europe/Simferopol", "Europe/Skopje", "Europe/Sofia", "Europe/Stockholm", "Europe/Tallinn", "Europe/Tirane", "Europe/Ulyanovsk", "Europe/Uzhgorod", "Europe/Vaduz", "Europe/Vatican", "Europe/Vienna", "Europe/Vilnius", "Europe/Volgograd", "Europe/Warsaw", "Europe/Zagreb", "Europe/Zaporozhye", "Europe/Zurich", "Indian/Antananarivo", "Indian/Chagos", "Indian/Christmas", "Indian/Cocos", "Indian/Comoro", "Indian/Kerguelen", "Indian/Mahe", "Indian/Maldives", "Indian/Mauritius", "Indian/Mayotte", "Indian/Reunion", "Pacific/Apia", "Pacific/Auckland", "Pacific/Bougainville", "Pacific/Chatham", "Pacific/Chuuk", "Pacific/Easter", "Pacific/Efate", "Pacific/Enderbury", "Pacific/Fakaofo", "Pacific/Fiji", "Pacific/Funafuti", "Pacific/Galapagos", "Pacific/Gambier", "Pacific/Guadalcanal", "Pacific/Guam", "Pacific/Honolulu", "Pacific/Kiritimati", "Pacific/Kosrae", "Pacific/Kwajalein", "Pacific/Majuro", "Pacific/Marquesas", "Pacific/Midway", "Pacific/Nauru", "Pacific/Niue", "Pacific/Norfolk", "Pacific/Noumea", "Pacific/Pago_Pago", "Pacific/Palau", "Pacific/Pitcairn", "Pacific/Pohnpei", "Pacific/Port_Moresby", "Pacific/Rarotonga", "Pacific/Saipan", "Pacific/Tahiti", "Pacific/Tarawa", "Pacific/Tongatapu", "Pacific/Wake", "Pacific/Wallis", "UTC"]}, "cms-payload.enum_events_link_target": {"name": "enum_events_link_target", "schema": "cms-payload", "values": ["_self", "_blank"]}, "cms-payload.enum_events_main_cta_target": {"name": "enum_events_main_cta_target", "schema": "cms-payload", "values": ["_self", "_blank"]}, "cms-payload.enum_events_status": {"name": "enum_events_status", "schema": "cms-payload", "values": ["draft", "published"]}, "cms-payload.enum__events_v_version_type": {"name": "enum__events_v_version_type", "schema": "cms-payload", "values": ["conference", "hackathon", "launch-week", "meetup", "office-hours", "talk", "webinar", "workshop", "other"]}, "cms-payload.enum__events_v_version_timezone": {"name": "enum__events_v_version_timezone", "schema": "cms-payload", "values": ["Africa/Abidjan", "Africa/Accra", "Africa/Addis_Ababa", "Africa/Algiers", "Africa/Asmara", "Africa/Bamako", "Africa/Bangui", "Africa/Banjul", "Africa/Bissau", "Africa/Blantyre", "Africa/Brazzaville", "Africa/Bujumbura", "Africa/Cairo", "Africa/Casablanca", "Africa/Ceuta", "Africa/Conakry", "Africa/Dakar", "Africa/Dar_es_Salaam", "Africa/Djibouti", "Africa/Douala", "Africa/El_Aaiun", "Africa/Freetown", "Africa/Gaborone", "Africa/Harare", "Africa/Johannesburg", "Africa/Juba", "Africa/Kampala", "Africa/Khartoum", "Africa/Kigali", "Africa/Kinshasa", "Africa/Lagos", "Africa/Libreville", "Africa/Lome", "Africa/Luanda", "Africa/Lubumbashi", "Africa/Lusaka", "Africa/Malabo", "Africa/Maputo", "Africa/Maseru", "Africa/Mbabane", "Africa/Mogadishu", "Africa/Monrovia", "Africa/Nairobi", "Africa/Ndjamena", "Africa/Niamey", "Africa/Nouakchott", "Africa/Ouagadougou", "Africa/Porto-Novo", "Africa/Sao_Tome", "Africa/Tripoli", "Africa/Tunis", "Africa/Windhoek", "America/Adak", "America/Anchorage", "America/Anguilla", "America/Antigua", "America/Araguaina", "America/Argentina/Buenos_Aires", "America/Argentina/Catamarca", "America/Argentina/Cordoba", "America/Argentina/Jujuy", "America/Argentina/La_Rioja", "America/Argentina/Mendoza", "America/Argentina/Rio_Gallegos", "America/Argentina/Salta", "America/Argentina/San_Juan", "America/Argentina/San_Luis", "America/Argentina/Tucuman", "America/Argentina/Ushuaia", "America/Aruba", "America/Asuncion", "America/Atikokan", "America/Bahia", "America/Bahia_Banderas", "America/Barbados", "America/Belem", "America/Belize", "America/Blanc-Sablon", "America/Boa_Vista", "America/Bogota", "America/Boise", "America/Cambridge_Bay", "America/Campo_Grande", "America/Cancun", "America/Caracas", "America/Cayenne", "America/Cayman", "America/Chicago", "America/Chihuahua", "America/Costa_Rica", "America/Creston", "America/Cuiaba", "America/Curacao", "America/Danmarkshavn", "America/Dawson", "America/Dawson_Creek", "America/Denver", "America/Detroit", "America/Dominica", "America/Edmonton", "America/Eirunepe", "America/El_Salvador", "America/Fort_Nelson", "America/Fortaleza", "America/Glace_Bay", "America/Godthab", "America/Goose_Bay", "America/Grand_Turk", "America/Grenada", "America/Guadeloupe", "America/Guatemala", "America/Guayaquil", "America/Guyana", "America/Halifax", "America/Havana", "America/Hermosillo", "America/Indiana/Indianapolis", "America/Indiana/Knox", "America/Indiana/Marengo", "America/Indiana/Petersburg", "America/Indiana/Tell_City", "America/Indiana/Vevay", "America/Indiana/Vincennes", "America/Indiana/Winamac", "America/Inuvik", "America/Iqaluit", "America/Jamaica", "America/Juneau", "America/Kentucky/Louisville", "America/Kentucky/Monticello", "America/Kralendijk", "America/La_Paz", "America/Lima", "America/Los_Angeles", "America/Lower_Princes", "America/Maceio", "America/Managua", "America/Manaus", "America/Marigot", "America/Martinique", "America/Matamoros", "America/Mazatlan", "America/Menominee", "America/Merida", "America/Metlakatla", "America/Mexico_City", "America/Miquelon", "America/Moncton", "America/Monterrey", "America/Montevideo", "America/Montserrat", "America/Nassau", "America/New_York", "America/Nipigon", "America/Nome", "America/Noronha", "America/North_Dakota/Beulah", "America/North_Dakota/Center", "America/North_Dakota/New_Salem", "America/Ojinaga", "America/Panama", "America/Pangnirtung", "America/Paramaribo", "America/Phoenix", "America/Port-au-Prince", "America/Port_of_Spain", "America/Porto_Velho", "America/Puerto_Rico", "America/Punta_Arenas", "America/Rainy_River", "America/Rankin_Inlet", "America/Recife", "America/Regina", "America/Resolute", "America/Rio_Branco", "America/Santarem", "America/Santiago", "America/Santo_Domingo", "America/Sao_Paulo", "America/Scoresbysund", "America/Sitka", "America/St_Barthelemy", "America/St_Johns", "America/St_Kitts", "America/St_Lucia", "America/St_Thomas", "America/St_Vincent", "America/Swift_Current", "America/Tegucigalpa", "America/Thule", "America/Thunder_Bay", "America/Tijuana", "America/Toronto", "America/Tortola", "America/Vancouver", "America/Whitehorse", "America/Winnipeg", "America/Yakutat", "America/Yellowknife", "Antarctica/Casey", "Antarctica/Davis", "Antarctica/DumontDUrville", "Antarctica/Macquarie", "Antarctica/Mawson", "Antarctica/McMurdo", "Antarctica/Palmer", "Antarctica/Rothera", "Antarctica/Syowa", "Antarctica/Troll", "Antarctica/Vostok", "Arctic/Longyearbyen", "Asia/Aden", "Asia/Almaty", "Asia/Amman", "Asia/Anadyr", "Asia/Aqtau", "Asia/Aqtobe", "Asia/Ashgabat", "Asia/Atyrau", "Asia/Baghdad", "Asia/Bahrain", "Asia/Baku", "Asia/Bangkok", "Asia/Barnaul", "Asia/Beirut", "Asia/Bishkek", "Asia/Brunei", "Asia/Chita", "Asia/Choibalsan", "Asia/Colombo", "Asia/Damascus", "Asia/Dhaka", "Asia/Dili", "Asia/Dubai", "Asia/Dushanbe", "Asia/Famagusta", "Asia/Gaza", "Asia/Hebron", "Asia/Ho_Chi_Minh", "Asia/Hong_Kong", "Asia/Hovd", "Asia/Irkutsk", "Asia/Istanbul", "Asia/Jakarta", "Asia/Jayapura", "Asia/Jerusalem", "Asia/Kabul", "Asia/Kamchatka", "Asia/Karachi", "Asia/Kathmandu", "Asia/Khandyga", "Asia/Kolkata", "Asia/Krasnoyarsk", "Asia/Kuala_Lumpur", "Asia/Kuching", "Asia/Kuwait", "Asia/Macau", "Asia/Magadan", "Asia/Makassar", "Asia/Manila", "Asia/Muscat", "Asia/Nicosia", "Asia/Novokuznetsk", "Asia/Novosibirsk", "Asia/Omsk", "Asia/Oral", "Asia/Phnom_Penh", "Asia/Pontianak", "Asia/Pyongyang", "Asia/Qatar", "Asia/Qostanay", "Asia/Qyzylorda", "Asia/Riyadh", "Asia/Sakhalin", "Asia/Samarkand", "Asia/Seoul", "Asia/Shanghai", "Asia/Singapore", "Asia/Srednekolymsk", "Asia/Taipei", "Asia/Tashkent", "Asia/Tbilisi", "Asia/Tehran", "Asia/Thimphu", "Asia/Tokyo", "Asia/Tomsk", "Asia/Ulaanbaatar", "Asia/Urumqi", "Asia/Ust-Nera", "Asia/Vientiane", "Asia/Vladivostok", "Asia/Yakutsk", "Asia/Yangon", "Asia/Yekaterinburg", "Asia/Yerevan", "Atlantic/Azores", "Atlantic/Bermuda", "Atlantic/Canary", "Atlantic/Cape_Verde", "Atlantic/Faroe", "Atlantic/Madeira", "Atlantic/Reykjavik", "Atlantic/South_Georgia", "Atlantic/St_Helena", "Atlantic/Stanley", "Australia/Adelaide", "Australia/Brisbane", "Australia/Broken_Hill", "Australia/Currie", "Australia/Darwin", "Australia/Eucla", "Australia/Hobart", "Australia/Lindeman", "Australia/Lord_Howe", "Australia/Melbourne", "Australia/Perth", "Australia/Sydney", "Europe/Amsterdam", "Europe/Andorra", "Europe/Astrakhan", "Europe/Athens", "Europe/Belgrade", "Europe/Berlin", "Europe/Bratislava", "Europe/Brussels", "Europe/Bucharest", "Europe/Budapest", "Europe/Busingen", "Europe/Chisinau", "Europe/Copenhagen", "Europe/Dublin", "Europe/Gibraltar", "Europe/Guernsey", "Europe/Helsinki", "Europe/Isle_of_Man", "Europe/Istanbul", "Europe/Jersey", "Europe/Kaliningrad", "Europe/Kiev", "Europe/Kirov", "Europe/Lisbon", "Europe/Ljubljana", "Europe/London", "Europe/Luxembourg", "Europe/Madrid", "Europe/Malta", "Europe/Mariehamn", "Europe/Minsk", "Europe/Monaco", "Europe/Moscow", "Europe/Oslo", "Europe/Paris", "Europe/Podgorica", "Europe/Prague", "Europe/Riga", "Europe/Rome", "Europe/Samara", "Europe/San_Marino", "Europe/Sarajevo", "Europe/Saratov", "Europe/Simferopol", "Europe/Skopje", "Europe/Sofia", "Europe/Stockholm", "Europe/Tallinn", "Europe/Tirane", "Europe/Ulyanovsk", "Europe/Uzhgorod", "Europe/Vaduz", "Europe/Vatican", "Europe/Vienna", "Europe/Vilnius", "Europe/Volgograd", "Europe/Warsaw", "Europe/Zagreb", "Europe/Zaporozhye", "Europe/Zurich", "Indian/Antananarivo", "Indian/Chagos", "Indian/Christmas", "Indian/Cocos", "Indian/Comoro", "Indian/Kerguelen", "Indian/Mahe", "Indian/Maldives", "Indian/Mauritius", "Indian/Mayotte", "Indian/Reunion", "Pacific/Apia", "Pacific/Auckland", "Pacific/Bougainville", "Pacific/Chatham", "Pacific/Chuuk", "Pacific/Easter", "Pacific/Efate", "Pacific/Enderbury", "Pacific/Fakaofo", "Pacific/Fiji", "Pacific/Funafuti", "Pacific/Galapagos", "Pacific/Gambier", "Pacific/Guadalcanal", "Pacific/Guam", "Pacific/Honolulu", "Pacific/Kiritimati", "Pacific/Kosrae", "Pacific/Kwajalein", "Pacific/Majuro", "Pacific/Marquesas", "Pacific/Midway", "Pacific/Nauru", "Pacific/Niue", "Pacific/Norfolk", "Pacific/Noumea", "Pacific/Pago_Pago", "Pacific/Palau", "Pacific/Pitcairn", "Pacific/Pohnpei", "Pacific/Port_Moresby", "Pacific/Rarotonga", "Pacific/Saipan", "Pacific/Tahiti", "Pacific/Tarawa", "Pacific/Tongatapu", "Pacific/Wake", "Pacific/Wallis", "UTC"]}, "cms-payload.enum__events_v_version_link_target": {"name": "enum__events_v_version_link_target", "schema": "cms-payload", "values": ["_self", "_blank"]}, "cms-payload.enum__events_v_version_main_cta_target": {"name": "enum__events_v_version_main_cta_target", "schema": "cms-payload", "values": ["_self", "_blank"]}, "cms-payload.enum__events_v_version_status": {"name": "enum__events_v_version_status", "schema": "cms-payload", "values": ["draft", "published"]}, "cms-payload.enum_posts_launchweek": {"name": "enum_posts_launchweek", "schema": "cms-payload", "values": ["6", "7", "8", "x", "ga", "12", "13", "14", "15"]}, "cms-payload.enum_posts_status": {"name": "enum_posts_status", "schema": "cms-payload", "values": ["draft", "published"]}, "cms-payload.enum__posts_v_version_launchweek": {"name": "enum__posts_v_version_launchweek", "schema": "cms-payload", "values": ["6", "7", "8", "x", "ga", "12", "13", "14", "15"]}, "cms-payload.enum__posts_v_version_status": {"name": "enum__posts_v_version_status", "schema": "cms-payload", "values": ["draft", "published"]}, "cms-payload.enum_users_roles": {"name": "enum_users_roles", "schema": "cms-payload", "values": ["admin", "editor"]}, "cms-payload.enum_payload_jobs_log_task_slug": {"name": "enum_payload_jobs_log_task_slug", "schema": "cms-payload", "values": ["inline", "schedulePublish"]}, "cms-payload.enum_payload_jobs_log_state": {"name": "enum_payload_jobs_log_state", "schema": "cms-payload", "values": ["failed", "succeeded"]}, "cms-payload.enum_payload_jobs_task_slug": {"name": "enum_payload_jobs_task_slug", "schema": "cms-payload", "values": ["inline", "schedulePublish"]}}, "schemas": {"cms-payload": "cms-payload"}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}