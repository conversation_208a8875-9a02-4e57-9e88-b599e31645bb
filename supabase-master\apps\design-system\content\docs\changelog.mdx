---
title: Changelog
description: Latest updates and announcements.
toc: false
---

## 18th Oct 2024 - Update multi-select

Improved Multi Select with combobox, badge limit, and more examples.

- [Multi Select](/design-system/components/multi-select)

[PR](https://github.com/supabase/supabase/pull/29695)

## 12th June 2024 - <PERSON><PERSON> styles updated

- [`<Button />`](/design-system/docs/components/button) styles updated

[PR](https://github.com/supabase/supabase/pull/27055)

## 5th June 2024 - More Modal and Dialog examples

[PR](https://github.com/supabase/supabase/pull/26844)

## 3rd June 2024 - Add scroll example to NavigationMenu

- [Added scroll example](https://supabase-design-system.vercel.app/design-system/docs/components/navigation-menu#with-horizontal-scroll) to [`<NavigationMenu />`](/design-system/docs/components/navigation-menu)

## 25th May 2024 - TreeView component added

- [`<TreeView />`](/design-system/docs/components/tree-view) component added.

[PR](https://github.com/supabase/supabase/pull/26821)

## 24th May 2024 - FormItemLayout updated, and InfoTooltip added

- [`<FormItemLayout />`](/design-system/docs/fragments/form-item-layout) now supports new props, `BeforeLabel` and `AfterLabel`
- [`<InfoTooltip />`](/design-system/docs/fragments/info-tooltip) added to easily add information tooltips

[PR](https://github.com/supabase/supabase/pull/26712)

## 24th May 2024 - Added Admonition component

- [Admonition](/design-system/docs/fragments/admonition)

[PR](https://github.com/supabase/supabase/pull/26710)

## 24th May 2024 - New components and docs

### New components added

- [Multi Select](/design-system/components/multi-select)

[PR](https://github.com/supabase/supabase/pull/26719)

### New components documented

- [Radio Group Card](/design-system/docs/components/radio-group-card)
- [Radio Group Stacked](/design-system/docs/components/radio-group-stacked)
- [Info Tooltip](/design-system/docs/components/info-tooltip)
- [Admonition](/design-system/docs/components/admonition)

### New colors

`bg-surface-75` tailwind color fixed for Dark theme

## 22nd May 2024 - Introducing Design System

We're introducing [Supabase Design System](/design-system), a home for Design related resources for Supabase
