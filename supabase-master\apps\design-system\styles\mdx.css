/* [data-theme="light"] {
  display: block;
} */

/* [data-theme="dark"] {
  display: none;
} */

.dark [data-theme='light'] {
  display: none;
}

.dark [data-theme='dark'] {
  display: block;
}

[data-rehype-pretty-code-fragment] {
  @apply relative text-white;
}

[data-rehype-pretty-code-fragment] code {
  @apply grid min-w-full break-words rounded-none border-0 bg-transparent p-0;
  counter-reset: line;
  box-decoration-break: clone;
}

[data-rehype-pretty-code-fragment] .line {
  @apply px-4 min-h-[1rem] py-0.5 w-full inline-block;
}

[data-rehype-pretty-code-fragment] [data-line-numbers] .line {
  @apply px-2;
}

[data-rehype-pretty-code-fragment] [data-line-numbers] > .line::before {
  @apply text-foreground-muted text-xs;
  counter-increment: line;
  content: counter(line);
  display: inline-block;
  width: 1.8rem;
  margin-right: 1.4rem;
  text-align: right;
}

[data-rehype-pretty-code-fragment] .line--highlighted {
  @apply relative bg-surface-200 dark:bg-surface-100;
}

[data-rehype-pretty-code-fragment] .line--highlighted {
  @apply border-foreground-muted border-l-2;
}

[data-rehype-pretty-code-fragment] .line-highlighted span {
  @apply relative;
}

[data-rehype-pretty-code-fragment] .word--highlighted {
  @apply rounded-md bg-zinc-700/50 border-zinc-700/70 p-1;
}

.dark [data-rehype-pretty-code-fragment] .word--highlighted {
  @apply bg-zinc-900;
}

[data-rehype-pretty-code-title] {
  @apply mt-2 pt-6 px-4 text-sm font-medium;
}

[data-rehype-pretty-code-title] + pre {
  @apply mt-2;
}

.mdx > .steps:first-child > h3:first-child {
  @apply mt-0;
}

.steps > h3 {
  @apply mt-8 mb-4 text-base font-semibold;
}

/* .preview-grid-background {
  position: absolute;
  inset: 0;
  height: 100%;
  width: 100%;
  background-image: linear-gradient(to right, #80808012 1px, transparent 1px),
    linear-gradient(to bottom, #80808012 1px, transparent 1px);
  background-size: 24px 24px;
} */
