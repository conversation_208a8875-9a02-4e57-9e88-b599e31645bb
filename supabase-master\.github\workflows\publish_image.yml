name: Publish to Image Registry

on:
  # run this action every Monday at 04:00 UTC (Singapore noon)
  schedule:
    - cron: '0 4 * * 1'
  workflow_dispatch:

jobs:
  settings:
    runs-on: ubuntu-latest
    outputs:
      image_version: ${{ steps.meta.outputs.version }}
    steps:
      - id: meta
        uses: docker/metadata-action@v4
        with:
          images: |
            supabase/studio
          flavor: |
            latest=true
          tags: |
            type=sha,prefix={{date 'YYYY.MM.DD'}}-sha-,enable=${{ github.event_name == 'schedule' }}
            type=sha,prefix={{date 'YYYY.MM.DD'}}-sha-,enable=${{ github.event_name == 'workflow_dispatch' }}

  release_x86:
    needs: settings
    runs-on: ubuntu-latest
    timeout-minutes: 120
    env:
      arch: amd64
    outputs:
      image_digest: ${{ steps.build.outputs.digest }}
    steps:
      - id: meta
        uses: docker/metadata-action@v4
        with:
          images: |
            supabase/studio
          tags: |
            type=raw,value=${{ needs.settings.outputs.image_version }}_${{ env.arch }}

      - uses: docker/setup-buildx-action@v2

      - name: Login to <PERSON>er<PERSON><PERSON>
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - id: build
        uses: docker/build-push-action@v3
        with:
          push: true
          context: '{{defaultContext}}'
          file: apps/studio/Dockerfile
          target: production
          platforms: linux/${{ env.arch }}
          tags: ${{ steps.meta.outputs.tags }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  release_arm:
    needs: settings
    runs-on: arm-runner
    timeout-minutes: 120
    env:
      arch: arm64
    outputs:
      image_digest: ${{ steps.build.outputs.digest }}
    steps:
      - uses: actions/checkout@v3

      - id: meta
        uses: docker/metadata-action@v4
        with:
          images: |
            supabase/studio
          tags: |
            type=raw,value=${{ needs.settings.outputs.image_version }}_${{ env.arch }}

      - uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - uses: docker/setup-buildx-action@v2
        with:
          driver: docker
          driver-opts: |
            image=moby/buildkit:master
            network=host

      - id: build
        uses: docker/build-push-action@v3
        with:
          push: true
          context: .
          file: apps/studio/Dockerfile
          target: production
          platforms: linux/${{ env.arch }}
          tags: ${{ steps.meta.outputs.tags }}
          no-cache: true

  merge_manifest:
    needs:
      - settings
      - release_x86
      - release_arm
    runs-on: ubuntu-latest
    steps:
      - uses: docker/setup-buildx-action@v2

      - uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Merge multi-arch manifests
        run: |
          docker buildx imagetools create -t supabase/studio:${{ needs.settings.outputs.image_version }} \
          supabase/studio@${{ needs.release_x86.outputs.image_digest }} \
          supabase/studio@${{ needs.release_arm.outputs.image_digest }}
          docker buildx imagetools create -t supabase/studio:latest \
          supabase/studio@${{ needs.release_x86.outputs.image_digest }} \
          supabase/studio@${{ needs.release_arm.outputs.image_digest }}

  publish:
    needs:
      - settings
      - merge_manifest
    # Call workflow explicitly because events from actions cannot trigger more actions
    uses: ./.github/workflows/mirror.yml
    with:
      version: ${{ needs.settings.outputs.image_version }}
    secrets: inherit
